// 全局圆润 Dialog 样式 - 参考登录页面风格
:root {
  // Dialog 主题色彩 - 薰衣草紫色主题
  --dialog-primary-color: #9370db;
  --dialog-primary-light: #ba85d6;
  --dialog-bg-color: rgba(255, 255, 255, 0.95);
  --dialog-border-color: rgba(147, 112, 219, 0.15);
  --dialog-shadow-color: rgba(147, 112, 219, 0.15);
  --dialog-text-primary: #1a1f36;
  --dialog-text-secondary: #697386;
  --dialog-radius: 24px;
  --dialog-radius-small: 16px;
  --dialog-radius-button: 12px;
}

// 全局 Dialog 样式重写
.el-dialog {
  border-radius: var(--dialog-radius) !important;
  background: var(--dialog-bg-color) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--dialog-border-color) !important;
  box-shadow: 0 20px 40px var(--dialog-shadow-color), 
              0 8px 24px rgba(0, 0, 0, 0.12) !important;
  overflow: hidden;
  position: relative;
  
  // 渐变边框效果
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg,
      rgba(147, 112, 219, 0.2) 0%,
      rgba(186, 133, 214, 0.1) 50%,
      rgba(147, 112, 219, 0.2) 100%);
    border-radius: var(--dialog-radius);
    z-index: -1;
    opacity: 0.6;
  }

  // Dialog 头部样式
  .el-dialog__header {
    padding: 24px 32px 16px !important;
    background: transparent !important;
    border-bottom: 1px solid rgba(229, 231, 235, 0.3) !important;
    
    .el-dialog__title {
      font-size: 20px !important;
      font-weight: 600 !important;
      color: var(--dialog-text-primary) !important;
      background: linear-gradient(135deg, var(--dialog-primary-color) 0%, var(--dialog-primary-light) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.3px;
    }
    
    .el-dialog__headerbtn {
      top: 20px !important;
      right: 24px !important;
      width: 32px !important;
      height: 32px !important;
      background: rgba(249, 250, 251, 0.8) !important;
      border: 1px solid rgba(229, 231, 235, 0.5) !important;
      border-radius: 50% !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      
      &:hover {
        background: rgba(66, 99, 235, 0.1) !important;
        border-color: var(--dialog-primary-color) !important;
        transform: scale(1.1) !important;
      }
      
      .el-dialog__close {
        color: var(--dialog-text-secondary) !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        
        &:hover {
          color: var(--dialog-primary-color) !important;
        }
      }
    }
  }

  // Dialog 内容样式
  .el-dialog__body {
    padding: 24px 32px !important;
    color: var(--dialog-text-primary) !important;
    line-height: 1.6 !important;
    font-size: 14px !important;
  }

  // Dialog 底部样式
  .el-dialog__footer {
    padding: 16px 32px 32px !important;
    background: transparent !important;
    border-top: 1px solid rgba(229, 231, 235, 0.3) !important;
    text-align: right !important;
    
    .el-button {
      border-radius: var(--dialog-radius-button) !important;
      padding: 10px 20px !important;
      font-weight: 500 !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      position: relative;
      overflow: hidden;
      
      // 默认按钮样式
      &.el-button--default {
        background: rgba(249, 250, 251, 0.8) !important;
        border: 1px solid rgba(229, 231, 235, 0.8) !important;
        color: var(--dialog-text-primary) !important;
        
        &:hover {
          background: rgba(255, 255, 255, 1) !important;
          border-color: var(--dialog-primary-color) !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(147, 112, 219, 0.15) !important;
        }
      }
      
      // 主要按钮样式
      &.el-button--primary {
        background: linear-gradient(135deg, var(--dialog-primary-color) 0%, #7b68ee 100%) !important;
        border: none !important;
        color: white !important;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transform: translateX(-100%);
          transition: transform 0.5s ease;
        }
        
        &:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 16px rgba(147, 112, 219, 0.3) !important;
          
          &::before {
            transform: translateX(100%);
          }
        }
        
        &:active {
          transform: translateY(0) !important;
        }
      }
      
      // 危险按钮样式
      &.el-button--danger {
        background: linear-gradient(135deg, #f56c6c 0%, #e53e3e 100%) !important;
        border: none !important;
        color: white !important;
        
        &:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 16px rgba(245, 108, 108, 0.3) !important;
        }
      }
      
      // 警告按钮样式
      &.el-button--warning {
        background: linear-gradient(135deg, #e6a23c 0%, #d69e2e 100%) !important;
        border: none !important;
        color: white !important;
        
        &:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 16px rgba(230, 162, 60, 0.3) !important;
        }
      }
    }
  }
}

// Dialog 遮罩层样式
.el-overlay {
  background-color: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(4px) !important;
}

// Dialog 动画优化
.el-dialog.el-overlay-dialog {
  animation: dialogSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes dialogSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .el-dialog {
    margin: 20px !important;
    width: calc(100% - 40px) !important;
    border-radius: var(--dialog-radius-small) !important;
    
    .el-dialog__header,
    .el-dialog__body,
    .el-dialog__footer {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
    
    .el-dialog__title {
      font-size: 18px !important;
    }
  }
}

// 特殊类型的 Dialog 样式
.rounded-dialog {
  .el-dialog {
    border-radius: 32px !important;
    
    &::before {
      border-radius: 32px;
    }
  }
}

.compact-dialog {
  .el-dialog {
    .el-dialog__header {
      padding: 16px 24px 12px !important;
    }
    
    .el-dialog__body {
      padding: 16px 24px !important;
    }
    
    .el-dialog__footer {
      padding: 12px 24px 20px !important;
    }
  }
}
