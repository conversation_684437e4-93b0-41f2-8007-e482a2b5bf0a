// 圆润 Tooltip 样式 - 符合项目设计风格

// 全局 Tooltip 变量
:root {
  --tooltip-bg: rgba(255, 255, 255, 0.95);
  --tooltip-border: rgba(147, 112, 219, 0.15);
  --tooltip-shadow: rgba(147, 112, 219, 0.15);
  --tooltip-text: #1a1f36;
  --tooltip-radius: 16px;
  --tooltip-padding: 12px 16px;
  --tooltip-font-size: 13px;
  --tooltip-font-weight: 500;
  --tooltip-letter-spacing: 0.2px;
  --tooltip-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 圆润菜单 Tooltip 样式类
.rounded-menu-tooltip {
  background: var(--tooltip-bg) !important;
  border: 1px solid var(--tooltip-border) !important;
  border-radius: var(--tooltip-radius) !important;
  box-shadow: 
    0 12px 32px var(--tooltip-shadow), 
    0 4px 16px rgba(0, 0, 0, 0.08) !important;
  backdrop-filter: blur(20px) !important;
  padding: var(--tooltip-padding) !important;
  font-size: var(--tooltip-font-size) !important;
  font-weight: var(--tooltip-font-weight) !important;
  color: var(--tooltip-text) !important;
  letter-spacing: var(--tooltip-letter-spacing) !important;
  transition: var(--tooltip-transition) !important;
  transform: translateY(2px) scale(0.95);
  opacity: 0;
  position: relative;
  overflow: hidden;
  max-width: 200px;
  word-wrap: break-word;
  
  // 渐变边框效果
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, 
      rgba(105, 65, 198, 0.2) 0%, 
      rgba(159, 122, 234, 0.1) 50%,
      rgba(105, 65, 198, 0.2) 100%);
    border-radius: var(--tooltip-radius);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // 箭头样式优化
  &[data-popper-placement^="right"] {
    &::after {
      border-right-color: var(--tooltip-bg) !important;
      filter: drop-shadow(-2px 0 4px var(--tooltip-shadow));
    }
  }

  &[data-popper-placement^="left"] {
    &::after {
      border-left-color: var(--tooltip-bg) !important;
      filter: drop-shadow(2px 0 4px var(--tooltip-shadow));
    }
  }

  &[data-popper-placement^="top"] {
    &::after {
      border-top-color: var(--tooltip-bg) !important;
      filter: drop-shadow(0 2px 4px var(--tooltip-shadow));
    }
  }

  &[data-popper-placement^="bottom"] {
    &::after {
      border-bottom-color: var(--tooltip-bg) !important;
      filter: drop-shadow(0 -2px 4px var(--tooltip-shadow));
    }
  }
  
  // 悬停效果
  &:hover {
    transform: translateY(0) scale(1) !important;
    box-shadow: 
      0 16px 40px rgba(105, 65, 198, 0.2), 
      0 8px 24px rgba(0, 0, 0, 0.12) !important;
    
    &::before {
      opacity: 1;
    }
  }
}

// 紧凑型 Tooltip
.compact-tooltip {
  padding: 8px 12px !important;
  font-size: 12px !important;
  border-radius: 12px !important;
}

// 大型 Tooltip
.large-tooltip {
  padding: 16px 20px !important;
  font-size: 14px !important;
  border-radius: 20px !important;
  max-width: 300px;
}

// 成功状态 Tooltip
.success-tooltip {
  --tooltip-border: rgba(103, 194, 58, 0.2);
  --tooltip-shadow: rgba(103, 194, 58, 0.15);
  
  &::before {
    background: linear-gradient(135deg, 
      rgba(103, 194, 58, 0.2) 0%, 
      rgba(103, 194, 58, 0.1) 50%,
      rgba(103, 194, 58, 0.2) 100%);
  }
}

// 警告状态 Tooltip
.warning-tooltip {
  --tooltip-border: rgba(230, 162, 60, 0.2);
  --tooltip-shadow: rgba(230, 162, 60, 0.15);
  
  &::before {
    background: linear-gradient(135deg, 
      rgba(230, 162, 60, 0.2) 0%, 
      rgba(230, 162, 60, 0.1) 50%,
      rgba(230, 162, 60, 0.2) 100%);
  }
}

// 错误状态 Tooltip
.error-tooltip {
  --tooltip-border: rgba(245, 108, 108, 0.2);
  --tooltip-shadow: rgba(245, 108, 108, 0.15);
  
  &::before {
    background: linear-gradient(135deg, 
      rgba(245, 108, 108, 0.2) 0%, 
      rgba(245, 108, 108, 0.1) 50%,
      rgba(245, 108, 108, 0.2) 100%);
  }
}

// 信息状态 Tooltip
.info-tooltip {
  --tooltip-border: rgba(64, 158, 255, 0.2);
  --tooltip-shadow: rgba(64, 158, 255, 0.15);
  
  &::before {
    background: linear-gradient(135deg, 
      rgba(64, 158, 255, 0.2) 0%, 
      rgba(64, 158, 255, 0.1) 50%,
      rgba(64, 158, 255, 0.2) 100%);
  }
}

// 悬浮提示动画
@keyframes tooltipSlideIn {
  0% {
    transform: translateY(8px) scale(0.9);
    opacity: 0;
  }
  60% {
    transform: translateY(-2px) scale(1.02);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes tooltipSlideOut {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(4px) scale(0.95);
    opacity: 0;
  }
}

// 过渡动画类
.tooltip-fade-enter-active {
  animation: tooltipSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.tooltip-fade-leave-active {
  animation: tooltipSlideOut 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

// 响应式设计
@media (max-width: 768px) {
  .rounded-menu-tooltip,
  .compact-tooltip,
  .large-tooltip {
    font-size: 12px !important;
    padding: 8px 12px !important;
    border-radius: 12px !important;
    max-width: 150px;
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  :root {
    --tooltip-bg: rgba(26, 31, 54, 0.95);
    --tooltip-text: #e2e8f0;
    --tooltip-border: rgba(159, 122, 234, 0.2);
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .rounded-menu-tooltip {
    border-width: 2px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  .rounded-menu-tooltip {
    transition: opacity 0.2s ease !important;
    transform: none !important;
  }
  
  .tooltip-fade-enter-active,
  .tooltip-fade-leave-active {
    animation: none !important;
    transition: opacity 0.2s ease !important;
  }
}
