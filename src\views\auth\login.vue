<template>
  <div class="login">
    <AnimatedBackground />
    
    <section class="login-container">
      <div class="login-content">
        <div class="login-header">
          <div class="brand-logo">
            <img 
              src="https://cdn-icons-png.flaticon.com/512/6681/6681204.png" 
              alt="logo" 
              class="login-logo"
            />
            <span class="brand-text">SAP Portal</span>
          </div>
          <h1 class="login-title">登录到您的账户</h1>
          <p class="login-subtitle">欢迎回来！请输入您的账户信息</p>
        </div>

        <el-form
          ref="formRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
        >
          <div class="form-group">
            <label class="form-label">用户名</label>
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                :prefix-icon="User"
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>
            
            <label class="form-label">密码</label>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                show-password
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>
          </div>

          <div class="login-options">
            <el-checkbox v-model="rememberMe">
              <span class="checkbox-text">记住我</span>
            </el-checkbox>
            <el-link type="primary" @click="handleForgotPassword" class="forgot-link">忘记密码？</el-link>
          </div>

          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            <span class="button-text">登录</span>
            <!-- <el-icon class="button-icon"><ArrowRight /></el-icon> -->
          </el-button>
        </el-form>

        <!-- <div class="login-footer">
          <div class="social-login">
            <span class="divider">
              <span class="divider-line"></span>
              <span class="divider-text">或通过以下方式登录</span>
              <span class="divider-line"></span>
            </span>
            <div class="social-buttons">
              <button class="social-button">
                <img src="https://www.svgrepo.com/show/475647/google-color.svg" alt="Google" />
                <span>Google</span>
              </button>
              <button class="social-button">
                <img src="https://www.svgrepo.com/show/448234/github.svg" alt="GitHub" />
                <span>GitHub</span>
              </button>
            </div>
          </div>
          
          <p class="signup-text">
            还没有账号？
            <el-link type="primary" class="signup-link">立即注册</el-link>
          </p>
        </div> -->
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Lock} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { useUserStore } from '@/stores/user'
import type { LoginParams } from '@/stores/user'
import AnimatedBackground from '@/components/AnimatedBackground.vue'
import { useRouter } from 'vue-router'

// const title = import.meta.env.VITE_APP_TITLE || 'SAP Frontend'
const userStore = useUserStore()
const loading = ref(false)
const rememberMe = ref(false)

const loginForm = reactive<LoginParams>({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const formRef = ref<FormInstance>()
const router = useRouter()

const handleLogin = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true
        await userStore.login(loginForm)
        ElMessage.success('登录成功')
        router.push('/monitor/database')
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        loading.value = false
      }
    }
  })
}

const handleForgotPassword = () => {
  ElMessage.info('请联系管理员重置密码')
}
</script>

<style lang="less" scoped>
.login {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;

  &-container {
    width: 100%;
    max-width: 480px;
    margin: 2rem;
    position: relative;
    z-index: 1;
    perspective: 1000px;
  }

  &-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 48px;
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    transform-style: preserve-3d;
    animation: cardFloat 6s ease-in-out infinite;
  }

  &-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .brand-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32px;
    gap: 12px;

    .login-logo {
      width: 40px;
      height: 40px;
      object-fit: contain;
      animation: logoFloat 6s ease-in-out infinite;
    }

    .brand-text {
      font-size: 24px;
      font-weight: 700;
      background: linear-gradient(135deg, #4263eb 0%, #8e98f5 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: -0.5px;
    }
  }

  &-title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1f36;
    margin-bottom: 8px;
    opacity: 0;
    animation: fadeSlideUp 0.6s ease forwards;
  }

  &-subtitle {
    font-size: 16px;
    color: #697386;
    margin-bottom: 32px;
    opacity: 0;
    animation: fadeSlideUp 0.6s ease forwards 0.2s;
  }

  .form-group {
    margin-bottom: 24px;
    opacity: 0;
    animation: fadeSlideUp 0.6s ease forwards 0.4s;
  }

  .form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #1a1f36;
    margin-bottom: 8px;
  }

  :deep(.el-input) {
    --el-input-height: 48px;
    --el-input-border-radius: 12px;
    --el-input-border-color: #e5e7eb;
    --el-input-hover-border-color: #4263eb;
    --el-input-focus-border-color: #4263eb;

    .el-input__wrapper {
      background-color: rgba(249, 250, 251, 0.8);
      box-shadow: none;
      border: 1px solid var(--el-input-border-color);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-input-hover-border-color);
        transform: translateY(-1px);
      }

      &.is-focus {
        background-color: white;
        border-color: var(--el-input-focus-border-color);
        box-shadow: 0 0 0 4px rgba(66, 99, 235, 0.1);
        transform: translateY(-2px);
      }
    }

    .el-input__inner {
      font-size: 16px;
      color: #1a1f36;

      &::placeholder {
        color: #97a3b9;
      }
    }
  }

  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    opacity: 0;
    animation: fadeSlideUp 0.6s ease forwards 0.6s;

    .checkbox-text {
      font-size: 14px;
      color: #1a1f36;
    }

    .forgot-link {
      font-size: 14px;
      font-weight: 500;
      color: #4263eb;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .login-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, #9370db 0%, #7b68ee 100%);
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-bottom: 32px;
    opacity: 0;
    animation: fadeSlideUp 0.6s ease forwards 0.8s;
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transform: translateX(-100%);
      transition: transform 0.5s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(66, 99, 235, 0.2);

      &::before {
        transform: translateX(100%);
      }
    }

    &:active {
      transform: translateY(0);
    }

    .button-text {
      margin-right: 8px;
    }

    .button-icon {
      font-size: 18px;
      transition: transform 0.3s ease;
    }

    &:hover .button-icon {
      transform: translateX(4px);
    }
  }

  .login-footer {
    opacity: 0;
    animation: fadeSlideUp 0.6s ease forwards 1s;
  }

  .social-login {
    margin-bottom: 24px;

    .divider {
      display: flex;
      align-items: center;
      margin: 24px 0;
      
      &-line {
        flex: 1;
        height: 1px;
        background: rgba(229, 231, 235, 0.8);
      }
      
      &-text {
        padding: 0 16px;
        color: #97a3b9;
        font-size: 14px;
      }
    }

    .social-buttons {
      display: flex;
      gap: 16px;
      margin-top: 16px;
    }

    .social-button {
      flex: 1;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #1a1f36;
      cursor: pointer;
      transition: all 0.3s ease;

      img {
        width: 20px;
        height: 20px;
      }

      &:hover {
        background: white;
        border-color: #97a3b9;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }

  .signup-text {
    font-size: 14px;
    color: #697386;

    .signup-link {
      font-weight: 500;
      color: #4263eb;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0) rotateX(0) rotateY(0);
  }
  25% {
    transform: translateY(-10px) rotateX(2deg) rotateY(-2deg);
  }
  75% {
    transform: translateY(10px) rotateX(-2deg) rotateY(2deg);
  }
}

@keyframes logoSpin {
  from {
    transform: rotateY(0);
  }
  to {
    transform: rotateY(360deg);
  }
}

@keyframes fadeSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0) rotate(0);
  }
  25% {
    transform: translateY(-4px) rotate(5deg);
  }
  75% {
    transform: translateY(4px) rotate(-5deg);
  }
}

// 响应式设计
@media (max-width: 640px) {
  .login {
    &-container {
      margin: 1rem;
    }

    &-content {
      padding: 32px 24px;
    }

    &-title {
      font-size: 24px;
    }

    .social-buttons {
      flex-direction: column;
    }
  }
}
</style> 