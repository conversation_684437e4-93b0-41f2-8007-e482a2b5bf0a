import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import { Monitor, Setting, Document, Key } from '@element-plus/icons-vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/',
    component: MainLayout,
    redirect: '/monitor/database',
    children: [
      // 监控中心
      {
        path: 'monitor',
        name: 'Monitor',
        redirect: '/monitor/database',
        meta: { icon: Monitor },
        children: [
          {
            path: 'database',
            name: 'DatabaseMonitor',
            component: () => import('@/views/monitor/DatabaseMonitor.vue'),
            meta: { title: '数据库监控' }
          },
          {
            path: 'performance',
            name: 'PerformanceMonitor',
            component: () => import('@/views/monitor/PerformanceMonitor.vue'),
            meta: { title: '性能分析' }
          },
          {
            path: 'alerts',
            name: 'AlertsMonitor',
            component: () => import('@/views/monitor/AlertsMonitor.vue'),
            meta: { title: '告警管理' }
          }
        ]
      },
      // 系统管理
      {
        path: 'system',
        name: 'System',
        redirect: '/system/users',
        meta: { icon: Setting },
        children: [
          {
            path: 'users',
            name: 'Users',
            component: () => import('@/views/system/Users.vue'),
            meta: { title: '用户管理' }
          },
          {
            path: 'roles',
            name: 'Roles',
            component: () => import('@/views/system/Roles.vue'),
            meta: { title: '角色管理' }
          },
          {
            path: 'permissions',
            name: 'Permissions',
            component: () => import('@/views/system/Permissions.vue'),
            meta: { title: '权限管理' }
          }
        ]
      },
      // 数据管理
      {
        path: 'data',
        name: 'Data',
        redirect: '/data/dictionary',
        meta: { icon: Document },
        children: [
          {
            path: 'dictionary',
            name: 'Dictionary',
            component: () => import('@/views/data/Dictionary.vue'),
            meta: { title: '数据字典' }
          },
          {
            path: 'backup',
            name: 'Backup',
            component: () => import('@/views/data/Backup.vue'),
            meta: { title: '数据备份' }
          },
          {
            path: 'recovery',
            name: 'Recovery',
            component: () => import('@/views/data/Recovery.vue'),
            meta: { title: '数据恢复' }
          }
        ]
      },
      // 访问控制
      {
        path: 'access',
        name: 'Access',
        redirect: '/access/policies',
        meta: { icon: Key },
        children: [
          {
            path: 'policies',
            name: 'Policies',
            component: () => import('@/views/access/Policies.vue'),
            meta: { title: '访问策略' }
          },
          {
            path: 'audit',
            name: 'Audit',
            component: () => import('@/views/access/Audit.vue'),
            meta: { title: '安全审计' }
          },
          {
            path: 'logs',
            name: 'Logs',
            component: () => import('@/views/access/Logs.vue'),
            meta: { title: '操作日志' }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/monitor/database'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title || '数据库平台'} - SAP Frontend`

  // 检查是否需要登录
  const token = localStorage.getItem('token')
  if (to.path === '/login') {
    if (token) {
      next('/monitor/database')
    } else {
      next()
    }
  } else {
    if (token) {
      next()
    } else {
      next('/login')
    }
  }
})

export default router 