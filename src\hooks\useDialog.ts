import { ref } from 'vue'
import type { FormInstance } from 'element-plus'

export interface DialogConfig<T = any> {
  // 标题
  title: string
  // 宽度
  width?: string | number
  // 表单配置
  form?: {
    // 表单项配置
    fields: {
      prop: string
      label: string
      type: 'input' | 'select' | 'date' | 'daterange' | 'textarea' | 'radio' | 'checkbox'
      options?: { label: string; value: any }[]
      rules?: {
        required?: boolean
        message?: string
        trigger?: 'blur' | 'change'
        validator?: (rule: any, value: any, callback: any) => void
      }[]
      attrs?: Record<string, any>
    }[]
    // 表单数据
    data?: Partial<T>
    // 提交前的数据处理
    beforeSubmit?: (formData: any) => any
    // 提交后的回调
    afterSubmit?: () => void
  }
  // API配置
  api?: {
    create?: (data: any) => Promise<any>
    update?: (data: any) => Promise<any>
    detail?: (id: string | number) => Promise<any>
  }
}

export function useDialog<T = any>(config: DialogConfig<T>) {
  // 对话框显示状态
  const visible = ref(false)
  // 加载状态
  const loading = ref(false)
  // 表单实例
  const formRef = ref<FormInstance>()
  // 表单数据
  const formData = ref<any>(config.form?.data || {})
  // 当前编辑的记录ID
  const currentId = ref<string | number>('')

  // 打开对话框
  const open = async (id?: string | number) => {
    visible.value = true
    if (id && config.api?.detail) {
      loading.value = true
      try {
        currentId.value = id
        const data = await config.api.detail(id)
        formData.value = data
      } catch (error) {
        console.error('获取详情失败:', error)
      } finally {
        loading.value = false
      }
    } else {
      currentId.value = ''
      formData.value = config.form?.data || {}
    }
  }

  // 关闭对话框
  const close = () => {
    visible.value = false
    formRef.value?.resetFields()
  }

  // 提交表单
  const submit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      
      loading.value = true
      let submitData = { ...formData.value }

      // 提交前的数据处理
      if (config.form?.beforeSubmit) {
        submitData = config.form.beforeSubmit(submitData)
      }

      if (currentId.value) {
        // 更新
        if (config.api?.update) {
          await config.api.update({
            ...submitData,
            id: currentId.value
          })
        }
      } else {
        // 创建
        if (config.api?.create) {
          await config.api.create(submitData)
        }
      }

      // 提交后的回调
      config.form?.afterSubmit?.()
      
      close()
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    visible,
    loading,
    formRef,
    formData,
    currentId,
    open,
    close,
    submit
  }
} 