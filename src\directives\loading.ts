import type { Directive, DirectiveBinding } from 'vue'
import { ElLoading } from 'element-plus'
import type { LoadingInstance } from 'element-plus/es/components/loading/src/loading'

const loadingMap = new WeakMap<HTMLElement, LoadingInstance>()

export const vLoading: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const instance = ElLoading.service({
      target: el,
      fullscreen: false,
      text: binding.arg || '加载中...',
      background: 'rgba(255, 255, 255, 0.9)'
    })
    loadingMap.set(el, instance)

    if (!binding.value) {
      instance.close()
    }
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const instance = loadingMap.get(el)
    if (binding.value !== binding.oldValue) {
      if (binding.value) {
        instance?.close()
        const newInstance = ElLoading.service({
          target: el,
          fullscreen: false,
          text: binding.arg || '加载中...',
          background: 'rgba(255, 255, 255, 0.9)'
        })
        loadingMap.set(el, newInstance)
      } else {
        instance?.close()
      }
    }
  },

  unmounted(el: HTMLElement) {
    const instance = loadingMap.get(el)
    instance?.close()
    loadingMap.delete(el)
  }
} 