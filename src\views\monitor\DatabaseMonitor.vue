<template>
  <div class="database-monitor">
    <!-- 顶部统计卡片 -->
    <div class="stat-cards">
      <el-row :gutter="20">
        <el-col :span="6" v-for="stat in statistics" :key="stat.title">
          <div class="stat-card" :class="stat.type">
            <div class="stat-icon">
              <el-icon><component :is="stat.icon" /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
            <div class="stat-trend" :class="stat.trend">
              <el-icon><component :is="stat.trend === 'up' ? 'ArrowUpBold' : 'ArrowDownBold'" /></el-icon>
              {{ stat.percentage }}%
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据库列表 -->
    <div class="database-table">
      <div class="table-header">
        <div class="header-left">
          <h2>数据库实例列表</h2>
          <el-radio-group v-model="timeFilter" size="small">
            <el-radio-button label="today">今日</el-radio-button>
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
          </el-radio-group>
        </div>
        <div class="header-right">
          <el-input
            v-model="searchQuery"
            placeholder="搜索数据库实例"
            prefix-icon="Search"
            clearable
          />
          <el-button-group>
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="exportData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </el-button-group>
        </div>
      </div>

      <el-table
        :data="filteredTableData"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f8faff' }"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="expanded-content">
              <el-descriptions :column="3" border>
                <el-descriptions-item label="实例ID">{{ props.row.id }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">2024-03-21 10:00:00</el-descriptions-item>
                <el-descriptions-item label="最后更新">2024-03-21 14:30:25</el-descriptions-item>
                <el-descriptions-item label="IP地址">*************</el-descriptions-item>
                <el-descriptions-item label="端口">3306</el-descriptions-item>
                <el-descriptions-item label="字符集">utf8mb4</el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="实例名称" min-width="180">
          <template #default="{ row }">
            <div class="instance-name">
              <el-tag :type="getTypeTag(row.type)" size="small">{{ row.type }}</el-tag>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="connections" label="当前连接数" width="120" />
        <el-table-column prop="cpu" label="CPU使用率" width="180">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.cpu" 
              :status="getCpuStatus(row.cpu)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column prop="memory" label="内存使用率" width="180">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.memory" 
              :status="getMemoryStatus(row.memory)"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button type="primary" link @click="handleDetail(row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button type="primary" link @click="handleConfig(row)">
                <el-icon><Setting /></el-icon>
              </el-button>
              <el-button type="primary" link @click="handleBackup(row)">
                <el-icon><Download /></el-icon>
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Monitor, 
  Connection, 
  Warning, 
  DataLine,
  Search,
  Refresh,
  View,
  Setting,
  Download,
  ArrowUpBold,
  ArrowDownBold
} from '@element-plus/icons-vue'

// 统计数据
const statistics = [
  {
    title: '运行实例',
    value: '28',
    icon: 'Monitor',
    type: 'primary',
    trend: 'up',
    percentage: 12
  },
  {
    title: '活跃连接',
    value: '1,286',
    icon: 'Connection',
    type: 'success',
    trend: 'up',
    percentage: 8
  },
  {
    title: '告警数量',
    value: '3',
    icon: 'Warning',
    type: 'danger',
    trend: 'down',
    percentage: 32
  },
  {
    title: '数据传输量',
    value: '2.1 TB',
    icon: 'DataLine',
    type: 'info',
    trend: 'up',
    percentage: 24
  }
]

// 表格数据
const searchQuery = ref('')
const timeFilter = ref('today')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

const tableData = ref([
  {
    name: 'MySQL-Production',
    type: 'MySQL',
    version: '8.0.28',
    connections: 356,
    cpu: 78,
    memory: 82,
    status: 'running'
  },
  {
    name: 'PostgreSQL-Analytics',
    type: 'PostgreSQL',
    version: '14.2',
    connections: 89,
    cpu: 45,
    memory: 60,
    status: 'running'
  },
  {
    name: 'Oracle-Finance',
    type: 'Oracle',
    version: '19c',
    connections: 245,
    cpu: 92,
    memory: 88,
    status: 'warning'
  },
  {
    name: 'MongoDB-UserData',
    type: 'MongoDB',
    version: '5.0.6',
    connections: 178,
    cpu: 35,
    memory: 45,
    status: 'running'
  },
  {
    name: 'Redis-Cache',
    type: 'Redis',
    version: '6.2.6',
    connections: 2890,
    cpu: 65,
    memory: 72,
    status: 'running'
  },
  {
    name: 'MySQL-Testing',
    type: 'MySQL',
    version: '8.0.28',
    connections: 12,
    cpu: 15,
    memory: 25,
    status: 'stopped'
  }
])

const filteredTableData = computed(() => {
  return tableData.value.filter(item =>
    item.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    item.type.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 工具函数
const getTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    'MySQL': '',
    'PostgreSQL': 'success',
    'Oracle': 'warning',
    'MongoDB': 'info',
    'Redis': 'danger'
  }
  return typeMap[type] || ''
}

const getCpuStatus = (value: number) => {
  if (value >= 90) return 'exception'
  if (value >= 70) return 'warning'
  return 'success'
}

const getMemoryStatus = (value: number) => {
  if (value >= 90) return 'exception'
  if (value >= 70) return 'warning'
  return 'success'
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'running': 'success',
    'warning': 'warning',
    'stopped': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'running': '运行中',
    'warning': '告警',
    'stopped': '已停止'
  }
  return statusMap[status] || status
}

// 操作函数
const refreshData = () => {
  // TODO: 实现刷新功能
}

const exportData = () => {
  // TODO: 实现导出功能
}

const handleDetail = (row: any) => {
  // TODO: 实现查看详情功能
}

const handleConfig = (row: any) => {
  // TODO: 实现配置功能
}

const handleBackup = (row: any) => {
  // TODO: 实现备份功能
}
</script>

<style lang="less" scoped>
.database-monitor {
  .stat-cards {
    margin-bottom: 24px;

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      gap: 16px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(108, 93, 211, 0.05) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        flex-shrink: 0;
        transition: transform 0.3s ease;

        .el-icon {
          transition: transform 0.3s ease;
        }
      }

      &:hover .stat-icon {
        transform: scale(1.05);

        .el-icon {
          transform: rotate(15deg);
        }
      }

      .stat-content {
        flex: 1;
        min-width: 0;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #1a1f36;
          margin-bottom: 4px;
        }

        .stat-title {
          font-size: 14px;
          color: #64748b;
        }
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 6px;
        background: rgba(0, 0, 0, 0.04);

        &.up {
          color: #10b981;
          background: rgba(16, 185, 129, 0.1);
        }

        &.down {
          color: #ef4444;
          background: rgba(239, 68, 68, 0.1);
        }
      }

      &.primary .stat-icon {
        color: #6941C6;
        background: rgba(108, 93, 211, 0.1);
      }

      &.success .stat-icon {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }

      &.danger .stat-icon {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }

      &.info .stat-icon {
        color: #3b82f6;
        background: rgba(59, 130, 246, 0.1);
      }
    }
  }

  .database-table {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        h2 {
          font-size: 18px;
          font-weight: 600;
          color: #1a1f36;
          margin: 0;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-input {
          width: 240px;
        }
      }
    }

    .instance-name {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .expanded-content {
      padding: 20px;
    }

    .table-pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

// Element Plus 组件样式优化
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header {
    th {
      background: #f8faff !important;
      color: #1a1f36;
      font-weight: 600;
    }
  }

  .el-table__row {
    td {
      padding: 12px 0;
    }
  }
}

:deep(.el-progress) {
  .el-progress-bar__outer {
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.04);
  }

  .el-progress-bar__inner {
    border-radius: 4px;
    transition: all 0.3s ease;
  }
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.el-pagination) {
  .el-pagination__total {
    margin-right: 16px;
  }

  .el-pagination__sizes {
    margin-right: 16px;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    width: 120px;
    color: #64748b;
  }

  .el-descriptions__content {
    color: #1a1f36;
  }
}
</style> 