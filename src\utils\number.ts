/**
 * 格式化数字为千分位
 * @param num 数字
 * @param decimals 小数位数
 */
export function formatThousands(num: number | string, decimals: number = 2): string {
  const n = Number(num)
  if (isNaN(n)) return '0'
  
  const fixed = n.toFixed(decimals)
  const [integer, decimal] = fixed.split('.')
  
  const formatted = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return decimal ? `${formatted}.${decimal}` : formatted
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`
}

/**
 * 格式化百分比
 * @param num 数字
 * @param decimals 小数位数
 * @param withSymbol 是否带百分号
 */
export function formatPercent(num: number, decimals: number = 2, withSymbol: boolean = true): string {
  const percent = (num * 100).toFixed(decimals)
  return withSymbol ? `${percent}%` : percent
}

/**
 * 数字补零
 * @param num 数字
 * @param length 总长度
 */
export function padZero(num: number | string, length: number = 2): string {
  return String(num).padStart(length, '0')
}

/**
 * 随机数生成
 * @param min 最小值
 * @param max 最大值
 * @param decimals 小数位数
 */
export function random(min: number, max: number, decimals: number = 0): number {
  const num = Math.random() * (max - min) + min
  return Number(num.toFixed(decimals))
}

/**
 * 数字范围限制
 * @param num 数字
 * @param min 最小值
 * @param max 最大值
 */
export function clamp(num: number, min: number, max: number): number {
  return Math.min(Math.max(num, min), max)
}

/**
 * 格式化金额为中文大写
 * @param money 金额
 */
export function formatChineseMoney(money: number): string {
  const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  const cnIntRadice = ['', '拾', '佰', '仟']
  const cnIntUnits = ['', '万', '亿', '兆']
  const cnDecUnits = ['角', '分', '毫', '厘']
  const cnInteger = '整'
  const cnIntLast = '元'

  let integerNum: string
  let decimalNum: string
  let chineseStr = ''
  let parts: string[]

  // 转换为字符串，并去掉左边的0
  const moneyStr = Math.abs(money).toString().replace(/^0+/, '')

  if (moneyStr === '') {
    return cnNums[0] + cnIntLast + cnInteger
  }

  // 分离整数和小数部分
  parts = moneyStr.split('.')
  integerNum = parts[0]
  decimalNum = parts[1] || ''

  // 处理整数部分
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0
    const IntLen = integerNum.length
    for (let i = 0; i < IntLen; i++) {
      const n = parseInt(integerNum.substr(i, 1), 10)
      const p = IntLen - i - 1
      const q = p / 4
      const m = p % 4
      if (n === 0) {
        zeroCount++
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0]
        }
        zeroCount = 0
        chineseStr += cnNums[n] + cnIntRadice[m]
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q]
      }
    }
    chineseStr += cnIntLast
  }

  // 处理小数部分
  if (decimalNum) {
    const decLen = decimalNum.length
    for (let i = 0; i < decLen; i++) {
      const n = parseInt(decimalNum.substr(i, 1), 10)
      if (n !== 0) {
        chineseStr += cnNums[n] + cnDecUnits[i]
      }
    }
  }

  if (!chineseStr) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger
  } else if (!decimalNum) {
    chineseStr += cnInteger
  }

  return money < 0 ? `负${chineseStr}` : chineseStr
}

/**
 * 格式化数字为指定精度的科学计数法
 * @param num 数字
 * @param precision 精度
 */
export function toScientific(num: number, precision: number = 2): string {
  return num.toExponential(precision)
}

/**
 * 数字四舍五入到指定位数
 * @param num 数字
 * @param decimals 小数位数
 */
export function round(num: number, decimals: number = 0): number {
  const factor = Math.pow(10, decimals)
  return Math.round(num * factor) / factor
}

/**
 * 计算百分比
 * @param num 数字
 * @param total 总数
 * @param decimals 小数位数
 */
export function percentage(num: number, total: number, decimals: number = 2): number {
  if (total === 0) return 0
  return round((num / total) * 100, decimals)
} 