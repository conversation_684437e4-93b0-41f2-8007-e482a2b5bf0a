import { ref } from 'vue'
import type { UploadFile, UploadFiles } from 'element-plus'
import { ElMessage } from 'element-plus'

export interface UploadConfig {
  // 上传地址
  action: string
  // 请求头
  headers?: Record<string, string>
  // 上传时附带的额外参数
  data?: Record<string, any>
  // 上传的文件字段名
  name?: string
  // 是否支持多选
  multiple?: boolean
  // 是否自动上传
  autoUpload?: boolean
  // 文件大小限制（MB）
  maxSize?: number
  // 最大允许的文件数
  limit?: number
  // 接受上传的文件类型
  accept?: string
  // 文件列表的类型
  listType?: 'text' | 'picture' | 'picture-card'
  // 是否显示已上传文件列表
  showFileList?: boolean
  // 上传前的回调
  beforeUpload?: (file: File) => boolean | Promise<File>
  // 上传成功的回调
  onSuccess?: (response: any, file: UploadFile, files: UploadFiles) => void
  // 上传失败的回调
  onError?: (error: Error, file: UploadFile, files: UploadFiles) => void
  // 文件状态改变的回调
  onChange?: (file: UploadFile, files: UploadFiles) => void
  // 文件列表移除文件的回调
  onRemove?: (file: UploadFile, files: UploadFiles) => void
  // 点击文件列表中已上传的文件的回调
  onPreview?: (file: UploadFile) => void
  // 文件超出个数限制的回调
  onExceed?: (files: File[], uploadFiles: UploadFiles) => void
}

export function useUpload(config: UploadConfig) {
  // 文件列表
  const fileList = ref<UploadFile[]>([])
  // 上传实例
  const uploadRef = ref()
  // 上传状态
  const uploading = ref(false)

  // 处理上传之前
  const handleBeforeUpload = async (file: File) => {
    // 检查文件大小
    if (config.maxSize && file.size > config.maxSize * 1024 * 1024) {
      ElMessage.error(`文件大小不能超过 ${config.maxSize}MB`)
      return false
    }

    // 检查文件类型
    if (config.accept) {
      const types = config.accept.split(',')
      const fileType = file.type || file.name.split('.').pop()?.toLowerCase()
      if (!types.some(type => {
        // 处理 .jpg 这样的后缀
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type)
        }
        // 处理 image/* 这样的 MIME 类型
        if (type.includes('*')) {
          const [category] = type.split('/')
          return fileType?.startsWith(category)
        }
        return type === fileType
      })) {
        ElMessage.error('文件类型不正确')
        return false
      }
    }

    // 调用配置的 beforeUpload
    if (config.beforeUpload) {
      return await config.beforeUpload(file)
    }

    return true
  }

  // 处理上传成功
  const handleSuccess = (response: any, file: UploadFile, files: UploadFiles) => {
    uploading.value = false
    config.onSuccess?.(response, file, files)
  }

  // 处理上传失败
  const handleError = (error: Error, file: UploadFile, files: UploadFiles) => {
    uploading.value = false
    config.onError?.(error, file, files)
  }

  // 处理文件状态改变
  const handleChange = (file: UploadFile, files: UploadFiles) => {
    if (file.status === 'ready') {
      uploading.value = true
    }
    config.onChange?.(file, files)
  }

  // 处理移除文件
  const handleRemove = (file: UploadFile, files: UploadFiles) => {
    config.onRemove?.(file, files)
  }

  // 处理预览文件
  const handlePreview = (file: UploadFile) => {
    config.onPreview?.(file)
  }

  // 处理超出文件数限制
  const handleExceed = (files: File[], uploadFiles: UploadFiles) => {
    config.onExceed?.(files, uploadFiles)
  }

  // 手动上传
  const submit = () => {
    if (!uploadRef.value) return
    uploadRef.value.submit()
  }

  // 清空已上传的文件列表
  const clearFiles = () => {
    if (!uploadRef.value) return
    uploadRef.value.clearFiles()
  }

  // 中止文件上传
  const abort = (file: UploadFile) => {
    if (!uploadRef.value) return
    uploadRef.value.abort(file)
  }

  return {
    fileList,
    uploadRef,
    uploading,
    handleBeforeUpload,
    handleSuccess,
    handleError,
    handleChange,
    handleRemove,
    handlePreview,
    handleExceed,
    submit,
    clearFiles,
    abort
  }
} 