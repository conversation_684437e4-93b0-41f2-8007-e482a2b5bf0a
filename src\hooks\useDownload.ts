import { ref } from 'vue'
import { ElMessage } from 'element-plus'

export interface DownloadConfig {
  // 下载地址
  url: string
  // 文件名
  filename?: string
  // 请求头
  headers?: Record<string, string>
  // 请求参数
  params?: Record<string, any>
  // 下载开始的回调
  onStart?: () => void
  // 下载进度的回调
  onProgress?: (progress: number) => void
  // 下载完成的回调
  onSuccess?: (blob: Blob) => void
  // 下载失败的回调
  onError?: (error: Error) => void
}

export function useDownload(config: DownloadConfig) {
  const downloading = ref(false)
  const progress = ref(0)

  const download = async () => {
    if (downloading.value) return
    downloading.value = true
    progress.value = 0

    try {
      config.onStart?.()

      const response = await fetch(config.url, {
        method: 'GET',
        headers: config.headers,
      })

      if (!response.ok) {
        throw new Error('下载失败')
      }

      const reader = response.body?.getReader()
      const contentLength = Number(response.headers.get('Content-Length'))
      
      if (!reader) {
        throw new Error('不支持的下载方式')
      }

      let receivedLength = 0
      const chunks: Uint8Array[] = []

      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          break
        }

        chunks.push(value)
        receivedLength += value.length

        if (contentLength) {
          progress.value = (receivedLength / contentLength) * 100
          config.onProgress?.(progress.value)
        }
      }

      const blob = new Blob(chunks)
      config.onSuccess?.(blob)

      // 下载文件
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = config.filename || getFilenameFromResponse(response) || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('下载成功')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('下载失败')
      config.onError?.(error as Error)
    } finally {
      downloading.value = false
      progress.value = 0
    }
  }

  // 从响应头中获取文件名
  const getFilenameFromResponse = (response: Response): string | null => {
    const disposition = response.headers.get('content-disposition')
    if (!disposition) return null

    const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
    if (!filenameMatch) return null

    const filename = filenameMatch[1].replace(/['"]/g, '')
    return decodeURIComponent(filename)
  }

  return {
    downloading,
    progress,
    download
  }
} 