import { ref } from 'vue'
import { ElMessage } from 'element-plus'

export interface ClipboardOptions {
  // 复制成功的回调
  onSuccess?: (text: string) => void
  // 复制失败的回调
  onError?: (error: Error) => void
  // 是否自动显示提示信息
  showTip?: boolean
  // 成功提示文本
  successText?: string
  // 失败提示文本
  errorText?: string
}

export function useClipboard(options: ClipboardOptions = {}) {
  const {
    onSuccess,
    onError,
    showTip = true,
    successText = '复制成功',
    errorText = '复制失败'
  } = options

  const copied = ref(false)
  const text = ref('')

  // 复制文本
  const copy = async (value: string) => {
    if (!value) return false

    try {
      await navigator.clipboard.writeText(value)
      text.value = value
      copied.value = true

      if (showTip) {
        ElMessage.success(successText)
      }

      onSuccess?.(value)
      return true
    } catch (error) {
      copied.value = false

      if (showTip) {
        ElMessage.error(errorText)
      }

      onError?.(error as Error)
      return false
    } finally {
      // 1秒后重置状态
      setTimeout(() => {
        copied.value = false
      }, 1000)
    }
  }

  // 从剪贴板读取文本
  const read = async () => {
    try {
      const value = await navigator.clipboard.readText()
      text.value = value
      return value
    } catch (error) {
      console.error('读取剪贴板失败:', error)
      throw error
    }
  }

  return {
    copied,
    text,
    copy,
    read
  }
} 