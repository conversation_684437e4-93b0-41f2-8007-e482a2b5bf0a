<template>
  <div class="particle-background" ref="particleContainer"></div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

const particleContainer = ref<HTMLElement | null>(null)
let particles: Array<{
  x: number
  y: number
  size: number
  speedX: number
  speedY: number
}> = []
let animationFrame: number

const createParticles = (count: number) => {
  const container = particleContainer.value
  if (!container) return

  const { width, height } = container.getBoundingClientRect()
  particles = Array.from({ length: count }, () => ({
    x: Math.random() * width,
    y: Math.random() * height,
    size: Math.random() * 3 + 1,
    speedX: (Math.random() - 0.5) * 0.5,
    speedY: (Math.random() - 0.5) * 0.5
  }))
}

const drawParticles = () => {
  const container = particleContainer.value
  if (!container) return

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const { width, height } = container.getBoundingClientRect()
  canvas.width = width
  canvas.height = height

  // Clear previous canvas if exists
  const oldCanvas = container.querySelector('canvas')
  if (oldCanvas) {
    container.removeChild(oldCanvas)
  }
  container.appendChild(canvas)

  const animate = () => {
    ctx.clearRect(0, 0, width, height)
    particles.forEach((particle) => {
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fillStyle = 'rgba(255, 255, 255, 0.5)'
      ctx.fill()

      // Update position
      particle.x += particle.speedX
      particle.y += particle.speedY

      // Bounce off walls
      if (particle.x < 0 || particle.x > width) particle.speedX *= -1
      if (particle.y < 0 || particle.y > height) particle.speedY *= -1
    })

    // Draw connections
    particles.forEach((particle1, i) => {
      particles.slice(i + 1).forEach(particle2 => {
        const dx = particle1.x - particle2.x
        const dy = particle1.y - particle2.y
        const distance = Math.sqrt(dx * dx + dy * dy)

        if (distance < 100) {
          ctx.beginPath()
          ctx.moveTo(particle1.x, particle1.y)
          ctx.lineTo(particle2.x, particle2.y)
          ctx.strokeStyle = `rgba(255, 255, 255, ${0.2 * (1 - distance / 100)})`
          ctx.stroke()
        }
      })
    })

    animationFrame = requestAnimationFrame(animate)
  }

  animate()
}

onMounted(() => {
  createParticles(50)
  drawParticles()

  window.addEventListener('resize', () => {
    createParticles(50)
    drawParticles()
  })
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})
</script>

<style scoped>
.particle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
}
</style> 