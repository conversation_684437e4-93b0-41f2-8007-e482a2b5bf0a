import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse, AxiosHeaders } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

export interface RequestInterceptors<T = AxiosResponse> {
  // 请求拦截
  requestInterceptor?: (config: AxiosRequestConfig) => AxiosRequestConfig
  requestInterceptorCatch?: (error: any) => any
  // 响应拦截
  responseInterceptor?: (res: T) => T
  responseInterceptorCatch?: (error: any) => any
}

export interface RequestConfig<T = AxiosResponse> extends AxiosRequestConfig {
  interceptors?: RequestInterceptors<T>
  showErrorMessage?: boolean // 是否显示错误信息
  loading?: boolean // 是否显示loading
  reLogin?: boolean // 是否重新登录
}

class Request {
  instance: AxiosInstance
  interceptors?: RequestInterceptors
  loading?: boolean
  showErrorMessage: boolean
  reLogin: boolean

  constructor(config: RequestConfig) {
    this.instance = axios.create(config)
    this.interceptors = config.interceptors
    this.showErrorMessage = config.showErrorMessage ?? true
    this.loading = config.loading ?? false
    this.reLogin = config.reLogin ?? true

    // 全局请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const userStore = useUserStore()
        const token = userStore.token
        if (token) {
          config.headers = AxiosHeaders.from(config.headers || {})
          config.headers.set('Authorization', `Bearer ${token}`)
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 全局响应拦截器
    this.instance.interceptors.response.use(
      (res) => {
        const { code, message, data } = res.data

        // 请求成功
        if (code === 0 || code === 200) {
          return data
        }

        // token过期
        if (code === 401) {
          if (this.reLogin) {
            ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
              confirmButtonText: '重新登录',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                const userStore = useUserStore()
                userStore.logout()
                router.push('/login')
              })
              .catch(() => {})
          }
          return Promise.reject(new Error(message || 'Token 过期'))
        }

        // 其他错误
        if (this.showErrorMessage) {
          ElMessage.error(message || '系统错误')
        }
        return Promise.reject(new Error(message || '系统错误'))
      },
      (error) => {
        if (this.showErrorMessage) {
          ElMessage.error(error.message || '系统错误')
        }
        return Promise.reject(error)
      }
    )
  }

  // 提供 axios 实例的方法
  get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }

  // 允许直接调用实例
  request<T = any>(config: RequestConfig): Promise<T> {
    return this.instance.request(config)
  }
}

// Create a default instance
const request = new Request({
  baseURL: import.meta.env.VITE_API as string || '',
  timeout: 10000,
  showErrorMessage: true,
  reLogin: true
})

export default request