<template>
  <div class="main-layout">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="header-bg"></div>
      <div class="lavender-group">
        <!-- 紫色花瓣飘落动画 - 增加数量，重点加强中左区域 -->
        <div
          class="purple-petal"
          v-for="i in 35"
          :key="'purple-petal-' + i"
          :style="{
            left: (30 + Math.random() * 50) + '%',
            animationDelay: (Math.random() * 3) + 's',
            animationDuration: (8 + Math.random() * 15) + 's',
            '--random-speed': Math.random(),
            '--random-drift': (Math.random() - 0.5) * 20 + 'px',
            '--start-position': (-100 - Math.random() * 200) + 'px'
          }"
        ></div>


      </div>
      <div class="header-content">
        <div class="header-left">
          <div class="brand">
            <img 
              src="https://cdn-icons-png.flaticon.com/512/8364/8364359.png" 
              alt="logo" 
              class="brand-logo"
            />
            <h1 class="brand-title" data-text="数据库平台">数据库平台</h1>
          </div>
          <div class="divider"></div>
          <el-button 
              class="collapse-btn" 
              @click="toggleCollapse"
            >
              <el-icon>
                <Fold v-if="!isCollapsed"/>
                <Expand v-else/>
              </el-icon>
            </el-button>
        </div>

        <div class="header-right">
          <div class="header-actions">
            <el-tooltip
              effect="light"
              content="消息通知"
              placement="bottom"
              :popper-class="'rounded-menu-tooltip'"
              :show-after="300"
              :hide-after="100"
            >
              <el-badge :value="3" class="action-item">
                <el-button class="action-button" text>
                  <el-icon><Bell /></el-icon>
                </el-button>
              </el-badge>
            </el-tooltip>
            <el-tooltip
              effect="light"
              content="帮助文档"
              placement="bottom"
              :popper-class="'rounded-menu-tooltip'"
              :show-after="300"
              :hide-after="100"
            >
              <el-button class="action-button" text>
                <el-icon><QuestionFilled /></el-icon>
              </el-button>
            </el-tooltip>
          </div>

          <el-dropdown trigger="hover" class="user-dropdown">
            <div class="user-info">
              <div class="user-avatar">
                <span class="avatar-text">{{ userStore.username.charAt(0).toUpperCase() }}</span>
              </div>
              <span class="user-name">{{ userStore.username }}</span>
              <el-icon class="dropdown-icon"><CaretBottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu class="user-dropdown-menu">
                <el-dropdown-item @click="handleCommand('account')">
                  <el-icon><User /></el-icon>
                  <span>我的账户</span>
                </el-dropdown-item>
                <el-dropdown-item @click="handleCommand('security')">
                  <el-icon><Lock /></el-icon>
                  <span>安全中心</span>
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleCommand('logout')">
                  <el-icon><SwitchButton /></el-icon>
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主要内容区 -->
    <div class="main-container">
      <!-- 左侧菜单 -->
      <aside class="sidebar" :class="{ 'is-collapsed': isCollapsed }">
        <div class="menu-wrapper">
          <el-menu
            :default-active="activeMenu"
            :collapse="isCollapsed"
            :collapse-transition="false"
            class="main-menu"
            @select="handleSelect"
            :popper-append-to-body="false"
          >
            <template v-for="menu in menus" :key="menu.path">
              <el-tooltip
                v-if="isCollapsed"
                effect="light"
                :content="menu.title"
                placement="right"
                :popper-class="'rounded-menu-tooltip'"
                :popper-options="{
                  modifiers: [{
                    name: 'offset',
                    options: { offset: [0, 12] }
                  },{
                    name: 'preventOverflow',
                    options: { padding: 20 }
                  }]
                }"
                :show-after="300"
                :hide-after="100"
                :enterable="false"
                transition="tooltip-fade"
              >
                <el-sub-menu :index="menu.path" :popper-class="'menu-popper'">
                  <template #title>
                    <el-icon><component :is="menu.icon" /></el-icon>
                    <span>{{ menu.title }}</span>
                  </template>
                  <el-menu-item
                    v-for="submenu in menu.children"
                    :key="submenu.path"
                    :index="submenu.path"
                  >
                    {{ submenu.title }}
                  </el-menu-item>
                </el-sub-menu>
              </el-tooltip>
              <el-sub-menu v-else :index="menu.path" :popper-class="'menu-popper'">
                <template #title>
                  <el-icon><component :is="menu.icon" /></el-icon>
                  <span>{{ menu.title }}</span>
                </template>
                <el-menu-item
                  v-for="submenu in menu.children"
                  :key="submenu.path"
                  :index="submenu.path"
                >
                  {{ submenu.title }}
                </el-menu-item>
              </el-sub-menu>
            </template>
          </el-menu>
        </div>
      </aside>

      <!-- 右侧内容 -->
      <main class="content" :class="{ 'is-collapsed': isCollapsed }">
        <div class="content-header">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="currentMenu">{{ currentMenu.title }}</el-breadcrumb-item>
            <el-breadcrumb-item v-if="currentSubmenu">{{ currentSubmenu.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="content-body">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { 
  User, 
  Lock, 
  SwitchButton, 
  CaretBottom,
  Fold,
  Expand,
  Monitor,
  Setting,
  Document,
  Key,
  Bell,
  QuestionFilled
} from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const isCollapsed = ref(false)
const activeMenu = ref('/monitor/database')

const menus = [
  {
    title: '监控中心',
    path: '/monitor',
    icon: 'Monitor',
    children: [
      { title: '数据库监控', path: '/monitor/database' },
      { title: '性能分析', path: '/monitor/performance' },
      { title: '告警管理', path: '/monitor/alerts' }
    ]
  },
  {
    title: '系统管理',
    path: '/system',
    icon: 'Setting',
    children: [
      { title: '用户管理', path: '/system/users' },
      { title: '角色管理', path: '/system/roles' },
      { title: '权限管理', path: '/system/permissions' }
    ]
  },
  {
    title: '数据管理',
    path: '/data',
    icon: 'Document',
    children: [
      { title: '数据字典', path: '/data/dictionary' },
      { title: '数据备份', path: '/data/backup' },
      { title: '数据恢复', path: '/data/recovery' }
    ]
  },
  {
    title: '访问控制',
    path: '/access',
    icon: 'Key',
    children: [
      { title: '访问策略', path: '/access/policies' },
      { title: '安全审计', path: '/access/audit' },
      { title: '操作日志', path: '/access/logs' }
    ]
  }
]

// 获取当前菜单信息
const currentMenu = computed(() => {
  const path = route.path
  return menus.find(menu => path.startsWith(menu.path))
})

const currentSubmenu = computed(() => {
  const path = route.path
  const menu = currentMenu.value
  if (!menu) return null
  return menu.children.find(submenu => submenu.path === path)
})

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleSelect = (path: string) => {
  router.push(path)
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'account':
      router.push('/account')
      break
    case 'security':
      router.push('/security')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消操作
      }
      break
  }
}
</script>

<style lang="less" scoped>
.main-layout {
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(135deg, #f8faff 0%, #f3f6ff 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 64px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 1px 0 0 rgba(108, 93, 211, 0.06);
    border-bottom: 1px solid rgba(108, 93, 211, 0.08);
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, rgba(159, 122, 234, 0.1) 0%, rgba(108, 93, 211, 0.05) 100%);
      opacity: 0.5;
    }

    .header-bg {
      position: absolute;
      inset: -20px;
      background: url('https://images.unsplash.com/photo-1499002238440-d264edd596ec?q=80&w=2070') center/cover no-repeat;
      opacity: 0.1;
      filter: blur(2px);
      transform-origin: center;
      animation: headerBgFloat 20s ease-in-out infinite;

      &::after {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(to right,
          rgba(255, 255, 255, 0.95) 0%,
          rgba(255, 255, 255, 0.5) 50%,
          rgba(255, 255, 255, 0.95) 100%
        );
      }
    }

    .lavender-group {
      position: absolute;
      left: 25%;
      right: 15%;
      top: 0;
      bottom: 0;
      overflow: hidden;
      pointer-events: none;
      z-index: 0;

      // 紫色花瓣飘落效果
      .purple-petal {
        position: absolute;
        width: 8px;
        height: 12px;
        background: linear-gradient(135deg,
          rgba(186, 85, 211, 0.8) 0%,
          rgba(147, 112, 219, 0.6) 50%,
          rgba(138, 43, 226, 0.4) 100%);
        border-radius: 50% 20% 50% 20%;
        animation: purplePetalFall linear infinite;
        pointer-events: none;
        transform-origin: center;
        top: var(--start-position, -100px);

        // 花瓣高光效果
        &::before {
          content: '';
          position: absolute;
          top: 15%;
          left: 20%;
          width: 3px;
          height: 5px;
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.1) 100%);
          border-radius: 50% 20% 50% 20%;
        }

        // 花瓣阴影
        &::after {
          content: '';
          position: absolute;
          top: 60%;
          right: 20%;
          width: 2px;
          height: 3px;
          background: rgba(100, 50, 150, 0.3);
          border-radius: 50%;
          filter: blur(1px);
        }

        // 不同速度和样式的花瓣变化，精确位置分布
        &:nth-child(2n) {
          background: linear-gradient(135deg,
            rgba(200, 100, 220, 0.7) 0%,
            rgba(186, 85, 211, 0.5) 50%,
            rgba(147, 112, 219, 0.3) 100%);
          transform: scale(0.8) rotate(45deg);
          animation-name: purplePetalFallSlow;
          left: 15% !important; /* 中部偏左 */
        }

        &:nth-child(3n) {
          background: linear-gradient(135deg,
            rgba(147, 112, 219, 0.9) 0%,
            rgba(138, 43, 226, 0.6) 50%,
            rgba(75, 0, 130, 0.4) 100%);
          transform: scale(1.2) rotate(-30deg);
          animation-name: purplePetalFallFast;
          left: 70% !important; /* 中部偏右 */
        }

        &:nth-child(4n) {
          background: linear-gradient(135deg,
            rgba(221, 160, 221, 0.6) 0%,
            rgba(186, 85, 211, 0.4) 50%,
            rgba(147, 112, 219, 0.3) 100%);
          transform: scale(0.9) rotate(60deg);
          animation-name: purplePetalFallDrift;
          left: 25% !important; /* 中部偏左 */
        }

        &:nth-child(5n) {
          background: linear-gradient(135deg,
            rgba(186, 85, 211, 0.8) 0%,
            rgba(147, 112, 219, 0.5) 50%,
            rgba(138, 43, 226, 0.3) 100%);
          transform: scale(1.1) rotate(-45deg);
          animation-name: purplePetalFallSpiral;
          left: 60% !important; /* 中部偏右 */
        }

        &:nth-child(6n) {
          animation-name: purplePetalFallGentle;
          transform: scale(0.7);
          left: 45% !important; /* 正中央 */
        }

        &:nth-child(7n) {
          animation-name: purplePetalFallTurbulent;
          transform: scale(1.3);
          left: 35% !important; /* 中部偏左 */
        }

        &:nth-child(8n) {
          left: 55% !important; /* 中部偏右 */
        }

        &:nth-child(9n) {
          left: 20% !important; /* 中部偏左 */
        }

        &:nth-child(10n) {
          left: 75% !important; /* 中部偏右 */
        }

        // 增加更多中左区域的花瓣
        &:nth-child(11n) {
          left: 18% !important; /* 中左区域 */
        }

        &:nth-child(12n) {
          left: 32% !important; /* 中左区域 */
        }

        &:nth-child(13n) {
          left: 28% !important; /* 中左区域 */
        }

        &:nth-child(14n) {
          left: 42% !important; /* 中部区域 */
        }

        &:nth-child(15n) {
          left: 38% !important; /* 中左区域 */
        }

        &:nth-child(16n) {
          left: 65% !important; /* 中右区域 */
        }

        &:nth-child(17n) {
          left: 22% !important; /* 中左区域 */
        }

        &:nth-child(18n) {
          left: 48% !important; /* 中部区域 */
        }

        &:nth-child(19n) {
          left: 30% !important; /* 中左区域 */
        }

        &:nth-child(20n) {
          left: 68% !important; /* 中右区域 */
        }
      }
    }

    &-content {
      height: 100%;
      // max-width: 1920px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      z-index: 1;
    }

    &-left {
      display: flex;
      align-items: center;
      gap: 24px;

      .brand {
        display: flex;
        align-items: center;
        gap: 12px;
        position: relative;
        padding: 8px 0;

        &-logo {
          width: 32px;
          height: 32px;
          object-fit: contain;
          filter: drop-shadow(0 2px 4px rgba(108, 93, 211, 0.2));
          transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
          transform-origin: center;

          &:hover {
            transform: scale(1.08) rotate(8deg);
            filter: drop-shadow(0 4px 12px rgba(108, 93, 211, 0.3));
          }
        }

        &-title {
          font-size: 18px;
          font-weight: 600;
          background: linear-gradient(135deg, #6941C6 0%, #9F7AEA 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          letter-spacing: -0.5px;
          position: relative;
          transition: all 0.3s ease;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: -2px;
            height: 2px;
            background: linear-gradient(to right, #6941C6, #9F7AEA);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 2px;
          }
        }

        &:hover {
          .brand-title::before {
            transform: scaleX(1);
          }
        }
      }

      .divider {
        width: 1px;
        height: 24px;
        background: linear-gradient(180deg,
          rgba(108, 93, 211, 0.02) 0%,
          rgba(108, 93, 211, 0.08) 50%,
          rgba(108, 93, 211, 0.02) 100%
        );
      }

      .collapse-btn {
        width: 36px;
        height: 36px;
        border: none;
        color: #6941C6;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 8px;
        position: relative;
        overflow: hidden;
        background: transparent;

        &::before {
          content: '';
          position: absolute;
          inset: 0;
          background: rgba(108, 93, 211, 0.06);
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 8px;
        }

        &:hover {
          transform: translateY(-1px);
          
          &::before {
            opacity: 1;
          }

          .el-icon {
            transform: scale(1.1);
          }
        }

        .el-icon {
          font-size: 18px;
          transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
      }
    }

    &-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .action-button {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        color: #6941C6;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        background: transparent;

        &::before {
          content: '';
          position: absolute;
          inset: 0;
          background: rgba(108, 93, 211, 0.06);
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 8px;
        }

        &:hover {
          transform: translateY(-1px);
          
          &::before {
            opacity: 1;
          }

          .el-icon {
            transform: scale(1.1);
          }
        }

        .el-icon {
          font-size: 18px;
          transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
      }

      .user-dropdown {
        .user-info {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 6px 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(108, 93, 211, 0.06);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 8px;
          }

          &:hover {
            transform: translateY(-1px);
            
            &::before {
              opacity: 1;
            }

            .user-avatar {
              transform: scale(1.05);
            }

            .dropdown-icon {
              transform: rotate(180deg);
            }
          }

          .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #6941C6 0%, #9F7AEA 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            font-size: 14px;
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
          }

          .user-name {
            font-size: 14px;
            color: #1A1F36;
            font-weight: 500;
          }

          .dropdown-icon {
            font-size: 12px;
            color: #6941C6;
            margin-left: 4px;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }
        }
      }
    }
  }

  .main-container {
    margin-top: 64px;
    flex: 1;
    display: flex;
    height: calc(100vh - 64px);
    padding: 16px;
    gap: 16px;
    overflow: hidden;
  }

  .sidebar {
    position: relative;
    height: 100%;
    width: 220px;
    flex-shrink: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 999;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(108, 93, 211, 0.08);
    border: 1px solid rgba(108, 93, 211, 0.08);
    display: flex;
    flex-direction: column;

    &.is-collapsed {
      width: 64px;
    }

    .menu-wrapper {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(108, 93, 211, 0.1);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(108, 93, 211, 0.02);
      }
    }

    .main-menu {
      border-right: none;
      background: transparent;
      transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      
      :deep(.el-menu-item), :deep(.el-sub-menu__title) {
        height: 44px;
        line-height: 44px;
        color: #1A1F36;
        font-size: 14px;
        font-weight: 500;
        padding: 0 16px !important;
        margin: 4px 8px;
        border-radius: 8px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;

        &:hover {
          color: #9370db;
          background: rgba(147, 112, 219, 0.1);
        }

        .el-icon {
          font-size: 16px;
          margin-right: 12px;
          color: inherit;
          transition: color 0.2s ease;
          flex-shrink: 0;
        }
      }

      &.el-menu--collapse {
        width: 64px;
        
        :deep(.el-menu-item), :deep(.el-sub-menu__title) {
          padding: 0 !important;
          margin: 4px auto;
          width: 48px;
          min-width: 48px;
          justify-content: center;

          .el-icon {
            margin: 0;
            font-size: 18px;
          }

          span {
            display: none;
          }
        }

        :deep(.el-tooltip__trigger) {
          justify-content: center;
          padding: 0 !important;
        }
      }

      :deep(.el-menu-item.is-active) {
        color: #9370db;
        background: rgba(147, 112, 219, 0.15);
        font-weight: 600;

        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 12px;
          bottom: 12px;
          width: 3px;
          background: #9370db;
          border-radius: 0 4px 4px 0;
        }
      }

      :deep(.el-sub-menu.is-active) {
        > .el-sub-menu__title {
          color: #6941C6;
          font-weight: 600;
        }
      }

      :deep(.el-menu--inline) {
        .el-menu-item {
          padding: 0 16px 0 16px !important;
          margin: 4px 8px;
          font-weight: 500;
          position: relative;
          left: 24px;
          
          &:hover {
            background: rgba(108, 93, 211, 0.08);
          }
        }
      }
    }
  }

  .content {
    flex: 1;
    min-width: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(108, 93, 211, 0.08);
    border: 1px solid rgba(108, 93, 211, 0.08);
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &-header {
      padding: 16px 20px;
      border-bottom: 1px solid rgba(108, 93, 211, 0.08);
      background: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(10px);

      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: #4A5568;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.3s ease;

          &.is-link:hover {
            color: #6941C6;
            transform: translateX(2px);
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: #1A1F36;
          font-weight: 600;
        }

        .el-breadcrumb__separator {
          color: #CBD5E0;
        }
      }
    }

    &-body {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(108, 93, 211, 0.1);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(108, 93, 211, 0.02);
      }
    }
  }
}

@keyframes headerGlow {
  0% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 0.3;
    transform: scale(1) rotate(360deg);
  }
}

@keyframes floatingLavender {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

@keyframes shine {
  0% {
    transform: rotate(0) translate(-50%, -50%);
  }
  100% {
    transform: rotate(360deg) translate(-50%, -50%);
  }
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(2px);
  }
  50% {
    transform: translateY(4px);
  }
}

@keyframes flow {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes lavenderSway {
  0%, 100% {
    transform: rotate(0deg) translateY(0);
  }
  25% {
    transform: rotate(5deg) translateY(-5px) translateX(2px);
  }
  50% {
    transform: rotate(-3deg) translateY(3px) translateX(-2px);
  }
  75% {
    transform: rotate(4deg) translateY(-2px) translateX(1px);
  }
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 用户下拉菜单样式
:deep(.user-dropdown-menu) {
  padding: 8px !important;
  min-width: 180px !important;
  border-radius: 12px !important;
  border: none !important;
  background: #fff !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  margin-top: 8px !important;

  .el-dropdown-menu__item {
    padding: 10px 16px !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    color: #1A1F36 !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    margin: 0 !important;

    .el-icon {
      font-size: 16px !important;
      color: #6941C6 !important;
    }

    &:hover {
      background: #F9F5FF !important;
      color: #6941C6 !important;
    }

    &.el-dropdown-menu__item--divided {
      border-top: 1px solid #E2E8F0 !important;
      margin-top: 4px !important;
      padding-top: 12px !important;
    }
  }
}

// 菜单 Tooltip 样式
:deep(.el-popper.is-dark) {
  background: #1A1F36 !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;

  .el-popper__arrow::before {
    background: #1A1F36 !important;
    border: none !important;
  }
}

// 子菜单弹出样式
:deep(.el-menu--popup) {
  min-width: 200px !important;
  margin: 8px !important;
  padding: 8px !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(108, 93, 211, 0.08) !important;
  box-shadow: 
    0 4px 24px rgba(108, 93, 211, 0.12),
    0 0 1px rgba(108, 93, 211, 0.05) !important;
  border-radius: 16px !important;
  transform-origin: top !important;
  /* animation: menuPopIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important; */
  overflow: hidden !important;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(159, 122, 234, 0.08) 0%, rgba(108, 93, 211, 0.03) 100%);
    pointer-events: none;
    opacity: 0.5;
    z-index: 0;
  }

  .el-menu-item {
    height: 44px !important;
    line-height: 44px !important;
    padding: 0 16px !important;
    margin: 4px 8px !important;
    border-radius: 8px !important;
    color: #1A1F36 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    position: relative !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 1;

    &:hover {
      color: #6941C6 !important;
      background: rgba(108, 93, 211, 0.08) !important;
      transform: translateX(4px);
    }

    &.is-active {
      color: #6941C6 !important;
      background: rgba(108, 93, 211, 0.1) !important;
      font-weight: 600 !important;

      &::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 12px;
        bottom: 12px;
        width: 3px;
        background: linear-gradient(180deg, #6941C6 0%, #9F7AEA 100%);
        border-radius: 0 4px 4px 0;
        box-shadow: 0 2px 4px rgba(108, 93, 211, 0.2);
      }
    }
  }
}

// 超级圆润菜单提示框样式 - 完美圆润风格
:deep(.rounded-menu-tooltip) {
    background: rgba(255, 255, 255, 0.98) !important;
    border: none !important;
    border-radius: 24px !important;
    box-shadow:
      0 20px 60px rgba(105, 65, 198, 0.25),
      0 8px 32px rgba(105, 65, 198, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(30px) saturate(180%) !important;
    padding: 16px 20px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #1a1f36 !important;
    letter-spacing: 0.3px !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    transform: translateY(8px) scale(0.85);
    opacity: 0;
    position: relative;
    overflow: hidden;
    max-width: 200px;
    text-align: center;

    // 内部光泽效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
      transition: left 0.6s ease;
    }

    // 渐变边框光晕
    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg,
        rgba(105, 65, 198, 0.4) 0%,
        rgba(159, 122, 234, 0.3) 25%,
        rgba(147, 197, 253, 0.3) 50%,
        rgba(159, 122, 234, 0.3) 75%,
        rgba(105, 65, 198, 0.4) 100%);
      border-radius: 26px;
      z-index: -1;
      opacity: 0;
      transition: opacity 0.4s ease;
      filter: blur(1px);
    }

    // 悬停效果
    &:hover {
      transform: translateY(0) scale(1.05) !important;
      box-shadow:
        0 30px 80px rgba(105, 65, 198, 0.35),
        0 12px 40px rgba(105, 65, 198, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.9),
        inset 0 1px 0 rgba(255, 255, 255, 1) !important;

      &::before {
        left: 100%;
      }

      &::after {
        opacity: 1;
      }
    }
  }

// 箭头样式完全重写 - 圆润风格
:deep(.rounded-menu-tooltip[data-popper-placement^="right"]) {
  margin-left: 12px;

  &::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 4px 0 4px 4px;
    box-shadow:
      -2px -2px 8px rgba(105, 65, 198, 0.15),
      inset 1px 1px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-50%) rotate(45deg);
    z-index: -1;
  }
}

// 超级流畅的悬浮提示动画
@keyframes tooltipSlideIn {
  0% {
    transform: translateY(12px) scale(0.8) rotateX(-15deg);
    opacity: 0;
    filter: blur(2px);
  }
  40% {
    transform: translateY(-3px) scale(1.05) rotateX(5deg);
    opacity: 0.7;
    filter: blur(0.5px);
  }
  70% {
    transform: translateY(1px) scale(0.98) rotateX(-2deg);
    opacity: 0.9;
    filter: blur(0px);
  }
  100% {
    transform: translateY(0) scale(1) rotateX(0deg);
    opacity: 1;
    filter: blur(0px);
  }
}

@keyframes tooltipSlideOut {
  0% {
    transform: translateY(0) scale(1) rotateX(0deg);
    opacity: 1;
    filter: blur(0px);
  }
  100% {
    transform: translateY(8px) scale(0.9) rotateX(-10deg);
    opacity: 0;
    filter: blur(1px);
  }
}

// 菜单项悬停效果 - 简化版，去掉复杂动画
:deep(.el-menu-item), :deep(.el-sub-menu__title) {
  transition: all 0.2s ease;
  border-radius: 12px;
  margin: 2px 8px;
  position: relative;

  &:hover {
    background: rgba(147, 112, 219, 0.1) !important;
    color: #9370db;
  }
}

// 收起状态下的菜单项样式 - 简化版
.sidebar.is-collapsed {
  :deep(.el-menu-item), :deep(.el-sub-menu__title) {
    &:hover {
      background: rgba(147, 112, 219, 0.1) !important;
      color: #9370db;
    }
  }
}

@keyframes headerBgFloat {
  0%, 100% {
    transform: scale(1.1) translate(0);
  }
  25% {
    transform: scale(1.15) translate(-1%, -1%);
  }
  50% {
    transform: scale(1.1) translate(1%, 0);
  }
  75% {
    transform: scale(1.15) translate(0, 1%);
  }
}

@keyframes lavenderFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    bottom: -40px;
  }
  25% {
    transform: translate(5px, -5px) rotate(2deg) scale(1.02);
    bottom: -38px;
  }
  50% {
    transform: translate(-5px, -3px) rotate(-1deg) scale(0.98);
    bottom: -42px;
  }
  75% {
    transform: translate(3px, -6px) rotate(1deg) scale(1.01);
    bottom: -39px;
  }
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes menuPopIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 紫色花瓣飘落动画 - 从屏幕上方到下方的完整飘落
@keyframes purplePetalFall {
  0% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(25vh) rotate(90deg) scale(1.03);
    opacity: 1;
  }
  50% {
    transform: translateY(50vh) rotate(200deg) scale(0.97);
    opacity: 1;
  }
  75% {
    transform: translateY(75vh) rotate(320deg) scale(1.01);
    opacity: 1;
  }
  95% {
    transform: translateY(95vh) rotate(420deg) scale(0.9);
    opacity: 1;
  }
  100% {
    transform: translateY(110vh) rotate(450deg) scale(0.85);
    opacity: 0;
  }
}

// 慢速飘落 - 轻柔摆动
@keyframes purplePetalFallSlow {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(25vh) translateX(10px) rotate(60deg) scale(1.1);
    opacity: 1;
  }
  50% {
    transform: translateY(50vh) translateX(-5px) rotate(150deg) scale(0.9);
    opacity: 1;
  }
  75% {
    transform: translateY(75vh) translateX(8px) rotate(240deg) scale(1.05);
    opacity: 1;
  }
  95% {
    transform: translateY(95vh) translateX(-3px) rotate(300deg) scale(0.9);
    opacity: 1;
  }
  100% {
    transform: translateY(110vh) translateX(-3px) rotate(320deg) scale(0.85);
    opacity: 0;
  }
}

// 快速飘落 - 相对较快
@keyframes purplePetalFallFast {
  0% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 1;
  }
  20% {
    transform: translateY(25vh) rotate(150deg) scale(0.9);
    opacity: 1;
  }
  40% {
    transform: translateY(50vh) rotate(300deg) scale(1.1);
    opacity: 1;
  }
  60% {
    transform: translateY(75vh) rotate(450deg) scale(0.95);
    opacity: 1;
  }
  95% {
    transform: translateY(95vh) rotate(600deg) scale(0.85);
    opacity: 1;
  }
  100% {
    transform: translateY(110vh) rotate(630deg) scale(0.8);
    opacity: 0;
  }
}

// 漂移飘落 - 温和的左右摆动
@keyframes purplePetalFallDrift {
  0% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(25vh) translateX(15px) rotate(60deg) scale(1.05);
    opacity: 1;
  }
  45% {
    transform: translateY(45vh) translateX(-12px) rotate(150deg) scale(0.95);
    opacity: 1;
  }
  65% {
    transform: translateY(65vh) translateX(18px) rotate(240deg) scale(1.02);
    opacity: 1;
  }
  85% {
    transform: translateY(85vh) translateX(-8px) rotate(330deg) scale(0.98);
    opacity: 1;
  }
  95% {
    transform: translateY(95vh) translateX(3px) rotate(390deg) scale(0.9);
    opacity: 1;
  }
  100% {
    transform: translateY(110vh) translateX(3px) rotate(410deg) scale(0.85);
    opacity: 0;
  }
}

// 螺旋飘落 - 优雅的螺旋下降
@keyframes purplePetalFallSpiral {
  0% {
    transform: translateY(-20px) translateX(0px) rotate(0deg) scale(1);
    opacity: 0;
  }
  12% {
    opacity: 1;
  }
  30% {
    transform: translateY(25vh) translateX(12px) rotate(120deg) scale(1.1);
  }
  55% {
    transform: translateY(50vh) translateX(-15px) rotate(280deg) scale(0.9);
  }
  80% {
    transform: translateY(75vh) translateX(10px) rotate(440deg) scale(1.05);
  }
  88% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) translateX(-6px) rotate(600deg) scale(0.95);
    opacity: 0;
  }
}

// 轻柔飘落 - 温和缓慢
@keyframes purplePetalFallGentle {
  0% {
    transform: translateY(-35px) translateX(0px) rotate(0deg) scale(1);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  30% {
    transform: translateY(25vh) translateX(5px) rotate(30deg) scale(1.05);
  }
  60% {
    transform: translateY(50vh) translateX(-3px) rotate(90deg) scale(0.95);
  }
  80% {
    transform: translateY(75vh) translateX(2px) rotate(150deg) scale(1.02);
  }
  80% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) translateX(-1px) rotate(180deg) scale(0.9);
    opacity: 0;
  }
}

// 湍流飘落 - 不规则但较慢的摆动
@keyframes purplePetalFallTurbulent {
  0% {
    transform: translateY(-10px) translateX(0px) rotate(0deg) scale(1);
    opacity: 0;
  }
  6% {
    opacity: 1;
  }
  18% {
    transform: translateY(15vh) translateX(25px) rotate(80deg) scale(1.2);
  }
  35% {
    transform: translateY(30vh) translateX(-20px) rotate(180deg) scale(0.8);
  }
  50% {
    transform: translateY(50vh) translateX(18px) rotate(280deg) scale(1.3);
  }
  65% {
    transform: translateY(70vh) translateX(-12px) rotate(400deg) scale(0.9);
  }
  80% {
    transform: translateY(85vh) translateX(8px) rotate(520deg) scale(1.1);
  }
  94% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) translateX(-3px) rotate(640deg) scale(0.7);
    opacity: 0;
  }
}

/* 旧的薰衣草动画已移除，现在使用紫色花瓣飘落 */
</style>