<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :class="dialogClass"
    :before-close="handleClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :modal="modal"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    :open-delay="openDelay"
    :close-delay="closeDelay"
    :top="top"
    :modal-class="modalClass"
    :z-index="zIndex"
    :center="center"
    :align-center="alignCenter"
    :destroy-on-close="destroyOnClose"
    :draggable="draggable"
    :overflow="overflow"
  >
    <template #header v-if="$slots.header">
      <slot name="header" />
    </template>
    
    <slot />
    
    <template #footer v-if="$slots.footer || showDefaultFooter">
      <slot name="footer">
        <div v-if="showDefaultFooter" class="dialog-footer">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirm"
            :loading="confirmLoading"
          >
            {{ confirmText }}
          </el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  modelValue: boolean
  title?: string
  width?: string | number
  type?: 'default' | 'rounded' | 'compact'
  showDefaultFooter?: boolean
  confirmText?: string
  cancelText?: string
  confirmLoading?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  modal?: boolean
  lockScroll?: boolean
  customClass?: string
  openDelay?: number
  closeDelay?: number
  top?: string
  modalClass?: string
  zIndex?: number
  center?: boolean
  alignCenter?: boolean
  destroyOnClose?: boolean
  draggable?: boolean
  overflow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '提示',
  width: '500px',
  type: 'default',
  showDefaultFooter: false,
  confirmText: '确定',
  cancelText: '取消',
  confirmLoading: false,
  closeOnClickModal: true,
  closeOnPressEscape: true,
  showClose: true,
  modal: true,
  lockScroll: true,
  customClass: '',
  openDelay: 0,
  closeDelay: 0,
  top: '15vh',
  modalClass: '',
  zIndex: 2000,
  center: false,
  alignCenter: true,
  destroyOnClose: false,
  draggable: false,
  overflow: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': []
  'cancel': []
  'close': []
  'open': []
  'opened': []
  'closed': []
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const dialogClass = computed(() => {
  const classes = []
  if (props.type === 'rounded') {
    classes.push('rounded-dialog')
  } else if (props.type === 'compact') {
    classes.push('compact-dialog')
  }
  return classes.join(' ')
})

const handleClose = (done: () => void) => {
  emit('close')
  done()
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}
</script>

<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
