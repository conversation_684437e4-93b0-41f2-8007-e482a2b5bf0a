import type { Directive, DirectiveBinding } from 'vue'
// import type { Store } from 'pinia';
import { useUserStore } from '@/stores/user'

function checkPermission(el: HTMLElement, binding: DirectiveBinding<string[]>, store: ReturnType<typeof useUserStore>) {
  const requiredPermissions = binding.value;
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasPermission = requiredPermissions.some(permission => 
      store.permissions.includes(permission)
    );
    if (!hasPermission) {
      el.parentNode?.removeChild(el)
    }
  } else {
    throw new Error('need permissions! Like v-permission="[\'admin\',\'editor\']"')
  }
}

export const vPermission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding, useUserStore())
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding, useUserStore())
  }
} 