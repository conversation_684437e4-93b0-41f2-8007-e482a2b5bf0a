# SAP Frontend 圆润设计风格指南

## 概述

本项目采用现代化的圆润设计风格，参考登录页面的设计语言，为整个系统提供一致的视觉体验。

## 设计原则

### 1. 圆润边角
- 使用统一的圆角半径：12px（基础）、16px（大）、24px（超大）
- 避免尖锐的直角，营造温和友好的视觉感受

### 2. 渐变与透明
- 主色调：#4263eb 到 #8e98f5 的渐变
- 背景使用半透明效果：rgba(255, 255, 255, 0.95)
- 毛玻璃效果：backdrop-filter: blur(20px)

### 3. 微交互动画
- 使用 cubic-bezier(0.4, 0, 0.2, 1) 缓动函数
- 悬停时轻微上移：translateY(-2px)
- 按钮点击时的光泽扫过效果

## 组件样式

### 侧边栏菜单悬浮提示

收起状态下的菜单项悬浮提示已优化为圆润风格：

```vue
<el-tooltip
  effect="light"
  :content="menuTitle"
  placement="right"
  :popper-class="'rounded-tooltip'"
  :show-after="300"
  :hide-after="100"
  transition="tooltip-fade"
>
  <!-- 菜单项内容 -->
</el-tooltip>
```

特点：
- 圆角半径：16px
- 毛玻璃背景效果
- 渐变边框
- 平滑的进入/退出动画
- 悬停时的微妙缩放效果

### 全局 Dialog 样式

所有 Dialog 组件自动应用圆润风格：

```vue
<template>
  <!-- 使用封装的圆润 Dialog 组件 -->
  <RoundedDialog
    v-model="dialogVisible"
    title="确认操作"
    type="rounded"
    :show-default-footer="true"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <p>这是一个圆润风格的对话框</p>
  </RoundedDialog>
</template>
```

特点：
- 24px 圆角
- 毛玻璃背景
- 渐变边框效果
- 优化的按钮样式
- 平滑的出现动画

### 按钮样式

```vue
<template>
  <!-- 主要按钮 -->
  <el-button type="primary">主要操作</el-button>
  
  <!-- 默认按钮 -->
  <el-button>次要操作</el-button>
  
  <!-- 危险按钮 -->
  <el-button type="danger">删除</el-button>
</template>
```

特点：
- 12px 圆角
- 渐变背景（主要按钮）
- 悬停时上移效果
- 光泽扫过动画

### 输入框样式

```vue
<template>
  <el-input
    v-model="value"
    placeholder="请输入内容"
  />
</template>
```

特点：
- 12px 圆角
- 半透明背景
- 聚焦时的阴影效果
- 悬停时轻微上移

## 颜色系统

### 主色调（薰衣草紫色主题）
- 主色：#9370db（薰衣草紫）
- 主色浅色：#ba85d6（浅薰衣草紫）
- 主色深色：#7b68ee（深薰衣草紫）
- 文字主色：#1a1f36
- 文字次色：#697386
- 文字辅助色：#97a3b9

### 功能色
- 成功：#67c23a
- 警告：#e6a23c
- 危险：#f56c6c
- 信息：#909399

### 边框色
- 基础边框：#e5e7eb
- 浅色边框：#f3f4f6
- 更浅边框：#f9fafb

## 使用方法

### 1. 引入样式

样式文件已自动引入到全局，无需额外配置。

### 2. 使用圆润 Dialog

```vue
<script setup>
import RoundedDialog from '@/components/RoundedDialog.vue'

const dialogVisible = ref(false)

const handleConfirm = () => {
  // 确认逻辑
  dialogVisible.value = false
}
</script>

<template>
  <RoundedDialog
    v-model="dialogVisible"
    title="操作确认"
    type="rounded"
    :show-default-footer="true"
    @confirm="handleConfirm"
  >
    <p>确定要执行此操作吗？</p>
  </RoundedDialog>
</template>
```

### 3. 自定义样式类

```less
// 使用预定义的样式类
.my-component {
  border-radius: var(--border-radius-large);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 24px rgba(66, 99, 235, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(66, 99, 235, 0.15);
  }
}
```

## 响应式设计

所有组件都支持响应式设计：

- 桌面端：完整的圆角和效果
- 平板端：适中的圆角和效果
- 移动端：较小的圆角，优化的间距

## 最佳实践

1. **保持一致性**：使用统一的圆角半径和颜色系统
2. **适度使用动画**：避免过度的动画效果影响性能
3. **考虑可访问性**：确保足够的颜色对比度
4. **测试兼容性**：在不同浏览器中测试毛玻璃效果

## 注意事项

1. 毛玻璃效果在某些旧版浏览器中可能不支持
2. 过多的阴影和模糊效果可能影响性能
3. 在移动设备上适当减少动画效果以提升性能

## 更新日志

- v1.0.0: 初始版本，包含基础的圆润设计风格
- v1.1.0: 添加侧边栏菜单悬浮提示优化
- v1.2.0: 完善全局 Dialog 样式系统
