import { ref, onMounted, onUnmounted } from 'vue'

export function useFullscreen(target?: HTMLElement) {
  const isFullscreen = ref(false)

  // 检查是否支持全屏
  const checkFullscreenSupport = () => {
    return document.fullscreenEnabled ||
      (document as any).webkitFullscreenEnabled ||
      (document as any).mozFullScreenEnabled ||
      (document as any).msFullscreenEnabled
  }

  // 获取当前全屏元素
  const getFullscreenElement = () => {
    return document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement
  }

  // 请求全屏
  const requestFullscreen = async (element: HTMLElement = document.documentElement) => {
    if (!checkFullscreenSupport()) {
      throw new Error('当前环境不支持全屏')
    }

    try {
      if (element.requestFullscreen) {
        await element.requestFullscreen()
      } else if ((element as any).webkitRequestFullscreen) {
        await (element as any).webkitRequestFullscreen()
      } else if ((element as any).mozRequestFullScreen) {
        await (element as any).mozRequestFullScreen()
      } else if ((element as any).msRequestFullscreen) {
        await (element as any).msRequestFullscreen()
      }
    } catch (error) {
      console.error('进入全屏失败:', error)
      throw error
    }
  }

  // 退出全屏
  const exitFullscreen = async () => {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen()
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen()
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen()
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen()
      }
    } catch (error) {
      console.error('退出全屏失败:', error)
      throw error
    }
  }

  // 切换全屏
  const toggleFullscreen = async () => {
    if (isFullscreen.value) {
      await exitFullscreen()
    } else {
      await requestFullscreen(target)
    }
  }

  // 全屏变化事件处理
  const handleFullscreenChange = () => {
    isFullscreen.value = Boolean(getFullscreenElement())
  }

  // 监听全屏变化事件
  onMounted(() => {
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)
  })

  // 移除全屏变化事件监听
  onUnmounted(() => {
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
  })

  return {
    isFullscreen,
    toggleFullscreen,
    requestFullscreen,
    exitFullscreen
  }
} 