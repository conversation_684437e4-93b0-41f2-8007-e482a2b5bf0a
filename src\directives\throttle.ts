import type { Directive, DirectiveBinding } from 'vue'

interface ElType extends HTMLElement {
  __throttleHandler__?: {
    fn: (...args: any[]) => void
    timer: ReturnType<typeof setTimeout> | null
    lastTime: number
  }
}

function throttle(fn: (...args: any[]) => void, delay: number) {
  let lastTime = 0
  let timer: ReturnType<typeof setTimeout> | null = null

  return function (this: any, ...args: any[]) {
    const now = Date.now()

    if (lastTime && now < lastTime + delay) {
      if (timer) clearTimeout(timer)
      timer = setTimeout(() => {
        lastTime = now
        fn.apply(this, args)
      }, delay)
    } else {
      lastTime = now
      fn.apply(this, args)
    }
  }
}

export const vThrottle: Directive = {
  mounted(el: ElType, binding: DirectiveBinding) {
    if (typeof binding.value !== 'function') {
      throw new Error('callback must be a function')
    }

    const delay = Number(binding.arg) || 300
    el.__throttleHandler__ = {
      fn: binding.value,
      timer: null,
      lastTime: 0
    }

    el.addEventListener('click', function (...args) {
      if (!el.__throttleHandler__) return
      const { fn } = el.__throttleHandler__
      const throttledFn = throttle(fn, delay)
      throttledFn.apply(this, args)
    })
  },

  beforeUnmount(el: ElType) {
    if (el.__throttleHandler__?.timer) {
      clearTimeout(el.__throttleHandler__.timer)
    }
  }
} 