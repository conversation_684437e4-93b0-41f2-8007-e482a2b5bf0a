import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

export interface LoginParams {
  username: string
  password: string
}

export const useUserStore = defineStore(
  'user',
  () => {
    const token = ref<string>(localStorage.getItem('token') || '')
    const username = ref<string>(localStorage.getItem('username') || '')
    const permissions = ref<string[]>(JSON.parse(localStorage.getItem('permissions') || '[]'))

    // 登录
    const login = async (loginParams: LoginParams) => {
      try {
        // 这里模拟登录，实际项目中需要调用后端 API
        if (loginParams.username === 'admin' && loginParams.password === '123456') {
          const mockToken = 'mock-token-' + Date.now()
          token.value = mockToken
          username.value = loginParams.username
          // 设置默认权限
          permissions.value = loginParams.username === 'admin' ? ['admin', 'editor'] : ['editor']
          localStorage.setItem('token', mockToken)
          localStorage.setItem('username', loginParams.username)
          localStorage.setItem('permissions', JSON.stringify(permissions.value))
          return true
        } else {
          throw new Error('用户名或密码错误')
        }
      } catch (error) {
        ElMessage.error(error instanceof Error ? error.message : '登录失败')
        throw error
      }
    }

    // 登出
    const logout = async () => {
      token.value = ''
      username.value = ''
      permissions.value = []
      localStorage.removeItem('token')
      localStorage.removeItem('username')
      localStorage.removeItem('permissions')
    }

    return {
      token,
      username,
      permissions,
      login,
      logout
    }
  },
  {
    persist: {
      storage: localStorage,
    },
  }
)
