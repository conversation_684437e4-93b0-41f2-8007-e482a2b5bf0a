import { ref, onMounted } from 'vue'
import type { FormInstance } from 'element-plus'

export interface FormConfig<T = any> {
  // 表单项配置
  fields: {
    prop: string
    label: string
    type: 'input' | 'select' | 'date' | 'daterange' | 'textarea' | 'radio' | 'checkbox' | 'switch'
    options?: { label: string; value: any }[]
    rules?: {
      required?: boolean
      message?: string
      trigger?: 'blur' | 'change'
      validator?: (rule: any, value: any, callback: any) => void
    }[]
    attrs?: Record<string, any>
    // 是否禁用
    disabled?: boolean
    // 是否只读
    readonly?: boolean
    // 占位文本
    placeholder?: string
    // 是否显示
    visible?: boolean | ((form: T) => boolean)
  }[]
  // 表单数据
  data?: Partial<T>
  // 表单布局
  layout?: {
    labelWidth?: string | number
    labelPosition?: 'left' | 'right' | 'top'
    size?: 'large' | 'default' | 'small'
    inline?: boolean
  }
  // API配置
  api?: {
    submit?: (data: any) => Promise<any>
    detail?: (id: string | number) => Promise<any>
  }
  // 提交前的数据处理
  beforeSubmit?: (formData: any) => any
  // 提交后的回调
  afterSubmit?: () => void
}

export function useForm<T = any>(config: FormConfig<T>) {
  // 表单实例
  const formRef = ref<FormInstance>()
  // 表单数据
  const formData = ref<any>(config.data || {})
  // 加载状态
  const loading = ref(false)
  // 当前编辑的记录ID
  const currentId = ref<string | number>('')

  // 获取表单详情
  const getDetail = async (id: string | number) => {
    if (!config.api?.detail) return

    loading.value = true
    try {
      currentId.value = id
      const data = await config.api.detail(id)
      formData.value = data
    } catch (error) {
      console.error('获取详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    formRef.value?.resetFields()
    currentId.value = ''
    formData.value = config.data || {}
  }

  // 验证表单
  const validateForm = async () => {
    if (!formRef.value) return false
    try {
      await formRef.value.validate()
      return true
    } catch (error) {
      return false
    }
  }

  const validate = async (): Promise<boolean> => {
    if (formRef.value) {
      try {
        const valid = await formRef.value.validate();
        return valid;
      } catch (error) {
        console.error('Validation failed:', error);
        return false;
      }
    }
    return false;
  };

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value || !config.api?.submit) return

    try {
      await formRef.value.validate()
      
      loading.value = true
      let submitData = { ...formData.value }

      // 提交前的数据处理
      if (config.beforeSubmit) {
        submitData = config.beforeSubmit(submitData)
      }

      if (currentId.value) {
        submitData.id = currentId.value
      }

      await config.api.submit(submitData)

      // 提交后的回调
      config.afterSubmit?.()
      
      resetForm()
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取表单项是否可见
  const isFieldVisible = (field: FormConfig['fields'][0]) => {
    if (typeof field.visible === 'function') {
      return field.visible(formData.value)
    }
    return field.visible !== false
  }

  // 获取表单项是否禁用
  const isFieldDisabled = (field: FormConfig['fields'][0]) => {
    if (typeof field.disabled === 'function') {
      return field.disabled(formData.value)
    }
    return field.disabled === true
  }

  // 初始化
  onMounted(() => {
    if (config.data) {
      formData.value = { ...config.data }
    }
  })

  return {
    formRef,
    formData,
    loading,
    currentId,
    getDetail,
    resetForm,
    validateForm,
    submitForm,
    isFieldVisible,
    isFieldDisabled
  }
} 