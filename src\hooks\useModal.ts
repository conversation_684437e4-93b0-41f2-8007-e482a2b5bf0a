import { ElMessageBox, ElMessage } from 'element-plus'
import { ElMessageBox } from 'element-plus';

export interface ModalOptions {
  title?: string
  content?: string
  confirmButtonText?: string
  cancelButtonText?: string
  type?: 'success' | 'warning' | 'info' | 'error'
  showCancelButton?: boolean
  showClose?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  center?: boolean
  customClass?: string
  showSuccessMessage?: boolean
  successMessage?: string
}

export function useModal() {
  // 确认对话框
  const confirm = (content: string, options: ModalOptions = {}) => {
    return ElMessageBox.confirm(
      content,
      options.title || defaultOptions.title || '提示',
      {
        confirmButtonText: options.confirmButtonText || defaultOptions.confirmButtonText || '确定',
        cancelButtonText: options.cancelButtonText || defaultOptions.cancelButtonText || '取消',
        type: options.type || defaultOptions.type || 'warning',
        showCancelButton: options.showCancelButton ?? defaultOptions.showCancelButton ?? true,
        showClose: options.showClose ?? defaultOptions.showClose ?? true,
        closeOnClickModal: options.closeOnClickModal ?? defaultOptions.closeOnClickModal ?? false,
        closeOnPressEscape: options.closeOnPressEscape ?? defaultOptions.closeOnPressEscape ?? true,
        center: options.center ?? defaultOptions.center ?? false,
        customClass: options.customClass || defaultOptions.customClass
      }
    ).then(() => {
      if (options.showSuccessMessage ?? defaultOptions.showSuccessMessage ?? true) {
        ElMessage.success(options.successMessage || defaultOptions.successMessage || '操作成功')
      }
      return true
    }).catch(() => {
      return false
    })
  }

  // 提示对话框
  const alert = (content: string, options: ModalOptions = {}) => {
    return ElMessageBox.alert(
      content,
      options.title || defaultOptions.title || '提示',
      {
        confirmButtonText: options.confirmButtonText || defaultOptions.confirmButtonText || '确定',
        type: options.type || defaultOptions.type || 'info',
        showClose: options.showClose ?? defaultOptions.showClose ?? true,
        closeOnClickModal: options.closeOnClickModal ?? defaultOptions.closeOnClickModal ?? false,
        closeOnPressEscape: options.closeOnPressEscape ?? defaultOptions.closeOnPressEscape ?? true,
        center: options.center ?? defaultOptions.center ?? false,
        customClass: options.customClass || defaultOptions.customClass
      }
    ).then(() => {
      return true
    }).catch(() => {
      return false
    })
  }

  // 输入对话框
  const prompt = (content: string, options: ModalOptions = {}) => {
    return ElMessageBox.prompt(
      content,
      options.title || defaultOptions.title || '提示',
      {
        confirmButtonText: options.confirmButtonText || defaultOptions.confirmButtonText || '确定',
        cancelButtonText: options.cancelButtonText || defaultOptions.cancelButtonText || '取消',
        type: options.type || defaultOptions.type || 'info',
        showCancelButton: options.showCancelButton ?? defaultOptions.showCancelButton ?? true,
        showClose: options.showClose ?? defaultOptions.showClose ?? true,
        closeOnClickModal: options.closeOnClickModal ?? defaultOptions.closeOnClickModal ?? false,
        closeOnPressEscape: options.closeOnPressEscape ?? defaultOptions.closeOnPressEscape ?? true,
        center: options.center ?? defaultOptions.center ?? false,
        customClass: options.customClass || defaultOptions.customClass
      }
    ).then(({ value }) => {
      return value
    }).catch(() => {
      return null
    })
  }

  return {
    confirm: (message: string, title: string = '提示') => {
      return ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
    },
    alert,
    prompt
  }
} 