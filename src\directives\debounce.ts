import type { Directive, DirectiveBinding } from 'vue'

interface ElType extends HTMLElement {
  __debounceHandler__?: {
    fn: (...args: any[]) => void
    timer: ReturnType<typeof setTimeout> | null
  }
}

function debounce(fn: (...args: any[]) => void, delay: number) {
  let timer: ReturnType<typeof setTimeout> | null = null
  return function (this: any, ...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

export const vDebounce: Directive = {
  mounted(el: ElType, binding: DirectiveBinding) {
    if (typeof binding.value !== 'function') {
      throw new Error('callback must be a function')
    }

    const delay = Number(binding.arg) || 300
    el.__debounceHandler__ = {
      fn: binding.value,
      timer: null
    }

    el.addEventListener('click', function (...args) {
      if (!el.__debounceHandler__) return
      const { fn } = el.__debounceHandler__
      const debouncedFn = debounce(fn, delay)
      debouncedFn.apply(this, args)
    })
  },

  beforeUnmount(el: ElType) {
    if (el.__debounceHandler__?.timer) {
      clearTimeout(el.__debounceHandler__.timer)
    }
  }
} 