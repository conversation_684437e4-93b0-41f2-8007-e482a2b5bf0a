<template>
  <div class="animated-background">
    <div class="gradient-orbs">
      <div class="orb orb-1"></div>
      <div class="orb orb-2"></div>
      <div class="orb orb-3"></div>
    </div>
    <div class="particles" ref="particlesContainer"></div>
    <div class="grid-overlay"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

const particlesContainer = ref<HTMLElement | null>(null)
let animationFrame: number

interface Particle {
  x: number
  y: number
  size: number
  speedX: number
  speedY: number
  opacity: number
}

const particles: Particle[] = []
const PARTICLE_COUNT = 50
const CONNECTION_DISTANCE = 100

const createParticles = () => {
  const container = particlesContainer.value
  if (!container) return

  const { width, height } = container.getBoundingClientRect()

  for (let i = 0; i < PARTICLE_COUNT; i++) {
    particles.push({
      x: Math.random() * width,
      y: Math.random() * height,
      size: Math.random() * 2 + 1,
      speedX: (Math.random() - 0.5) * 0.5,
      speedY: (Math.random() - 0.5) * 0.5,
      opacity: Math.random() * 0.5 + 0.2
    })
  }
}

const drawParticles = () => {
  const container = particlesContainer.value
  if (!container) return

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const { width, height } = container.getBoundingClientRect()
  canvas.width = width
  canvas.height = height

  // Remove old canvas if exists
  const oldCanvas = container.querySelector('canvas')
  if (oldCanvas) {
    container.removeChild(oldCanvas)
  }
  container.appendChild(canvas)

  const animate = () => {
    ctx.clearRect(0, 0, width, height)

    // Update and draw particles
    particles.forEach(particle => {
      particle.x += particle.speedX
      particle.y += particle.speedY

      // Bounce off edges
      if (particle.x < 0 || particle.x > width) particle.speedX *= -1
      if (particle.y < 0 || particle.y > height) particle.speedY *= -1

      // Draw particle
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`
      ctx.fill()
    })

    // Draw connections
    particles.forEach((p1, i) => {
      particles.slice(i + 1).forEach(p2 => {
        const dx = p1.x - p2.x
        const dy = p1.y - p2.y
        const distance = Math.sqrt(dx * dx + dy * dy)

        if (distance < CONNECTION_DISTANCE) {
          ctx.beginPath()
          ctx.moveTo(p1.x, p1.y)
          ctx.lineTo(p2.x, p2.y)
          const opacity = (1 - distance / CONNECTION_DISTANCE) * 0.2
          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`
          ctx.stroke()
        }
      })
    })

    animationFrame = requestAnimationFrame(animate)
  }

  animate()
}

const handleResize = () => {
  const container = particlesContainer.value
  if (!container) return

  const { width, height } = container.getBoundingClientRect()
  particles.length = 0
  createParticles()

  const canvas = container.querySelector('canvas')
  if (canvas) {
    canvas.width = width
    canvas.height = height
  }
}

onMounted(() => {
  createParticles()
  drawParticles()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="less" scoped>
.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(125deg, #040440 0%, #0b0b3a 28.89%, #161657 75.52%);
  z-index: 0;

  .gradient-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
    filter: blur(40px);
    mix-blend-mode: soft-light;
    pointer-events: none;
  }

  .orb {
    position: absolute;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    opacity: 0.5;
    animation: orbFloat 20s infinite ease-in-out;

    &-1 {
      background: radial-gradient(circle at 50% 50%, rgba(76, 0, 255, 0.5), rgba(76, 0, 255, 0));
      top: -10%;
      right: -10%;
      animation-delay: -5s;
    }

    &-2 {
      background: radial-gradient(circle at 50% 50%, rgba(255, 0, 128, 0.5), rgba(255, 0, 128, 0));
      bottom: -20%;
      left: -10%;
      animation-delay: -2.5s;
    }

    &-3 {
      background: radial-gradient(circle at 50% 50%, rgba(0, 255, 255, 0.5), rgba(0, 255, 255, 0));
      top: 30%;
      right: 30%;
      animation-delay: 0s;
    }
  }

  .particles {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.5;
  }
}

@keyframes orbFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(5%, 5%) rotate(90deg);
  }
  50% {
    transform: translate(0, 10%) rotate(180deg);
  }
  75% {
    transform: translate(-5%, 5%) rotate(270deg);
  }
}
</style> 