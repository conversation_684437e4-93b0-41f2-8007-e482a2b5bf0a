import { ElMessage, ElNotification } from 'element-plus'
// import type { MessageParams, NotificationParams } from 'element-plus'

export interface MessageOptions {
  duration?: number
  showClose?: boolean
  customClass?: string
  dangerouslyUseHTMLString?: boolean
  zIndex?: number
  offset?: number
  onClose?: () => void
}

export interface NotifyOptions extends MessageOptions {
  title?: string
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  icon?: string
}

const defaultOptions: MessageOptions = {
  duration: 3000,
  showClose: false,
  dangerouslyUseHTMLString: false
}

export function useMessage() {
  // 成功提示
  const success = (message: string, options: MessageOptions = {}) => {
    ElMessage.success({
      message,
      duration: options.duration ?? defaultOptions.duration,
      showClose: options.showClose ?? defaultOptions.showClose,
      customClass: options.customClass,
      dangerouslyUseHTMLString: options.dangerouslyUseHTMLString ?? defaultOptions.dangerouslyUseHTMLString,
      zIndex: options.zIndex,
      offset: options.offset,
      onClose: options.onClose
    })
  }

  // 警告提示
  const warning = (message: string, options: MessageOptions = {}) => {
    ElMessage.warning({
      message,
      duration: options.duration ?? defaultOptions.duration,
      showClose: options.showClose ?? defaultOptions.showClose,
      customClass: options.customClass,
      dangerouslyUseHTMLString: options.dangerouslyUseHTMLString ?? defaultOptions.dangerouslyUseHTMLString,
      zIndex: options.zIndex,
      offset: options.offset,
      onClose: options.onClose
    })
  }

  // 信息提示
  const info = (message: string, options: MessageOptions = {}) => {
    ElMessage.info({
      message,
      duration: options.duration ?? defaultOptions.duration,
      showClose: options.showClose ?? defaultOptions.showClose,
      customClass: options.customClass,
      dangerouslyUseHTMLString: options.dangerouslyUseHTMLString ?? defaultOptions.dangerouslyUseHTMLString,
      zIndex: options.zIndex,
      offset: options.offset,
      onClose: options.onClose
    })
  }

  // 错误提示
  const error = (message: string, options: MessageOptions = {}) => {
    ElMessage.error({
      message,
      duration: options.duration ?? defaultOptions.duration,
      showClose: options.showClose ?? defaultOptions.showClose,
      customClass: options.customClass,
      dangerouslyUseHTMLString: options.dangerouslyUseHTMLString ?? defaultOptions.dangerouslyUseHTMLString,
      zIndex: options.zIndex,
      offset: options.offset,
      onClose: options.onClose
    })
  }

  // 通知提示
  const notify = (message: string, options: NotifyOptions = {}) => {
    ElNotification({
      message,
      title: options.title,
      duration: options.duration ?? defaultOptions.duration,
      position: options.position || 'top-right',
      showClose: options.showClose ?? defaultOptions.showClose,
      customClass: options.customClass,
      dangerouslyUseHTMLString: options.dangerouslyUseHTMLString ?? defaultOptions.dangerouslyUseHTMLString,
      icon: options.icon,
      offset: options.offset || 0,
      onClose: options.onClose,
      zIndex: options.zIndex
    })
  }

  return {
    success,
    error,
    warning,
    info,
    notify
  }
} 