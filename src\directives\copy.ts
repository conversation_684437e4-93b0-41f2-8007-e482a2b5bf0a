import type { Directive } from 'vue'
// import { ElMessage } from 'element-plus'

export const vCopy: Directive = {
  mounted(el, binding) {
    const copyHandler = async () => {
      try {
        const value = binding.value
        if (value === undefined || value === null) {
          throw new Error('需要复制内容')
        }

        const text = typeof value === 'object' ? JSON.stringify(value) : String(value)
        await navigator.clipboard.writeText(text)
      } catch (error) {
        console.error('复制失败:', error)
      }
    }

    el.addEventListener('click', copyHandler)
  }
}