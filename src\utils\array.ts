/**
 * 数组去重
 * @param arr 要去重的数组
 * @param key 如果是对象数组，可以指定根据对象的某个key去重
 */
export function uniqueArray<T>(arr: T[], key?: keyof T): T[] {
  if (key) {
    const map = new Map()
    return arr.filter(item => {
      const keyValue = item[key]
      if (!map.has(keyValue)) {
        map.set(keyValue, true)
        return true
      }
      return false
    })
  }
  return Array.from(new Set(arr))
}

/**
 * 数组分组
 * @param arr 要分组的数组
 * @param key 分组依据的key
 */
export function groupBy<T>(arr: T[], key: keyof T): Record<string, T[]> {
  return arr.reduce((groups, item) => {
    const groupKey = String(item[key])
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

/**
 * 数组扁平化
 * @param arr 要扁平化的数组
 * @param depth 扁平化深度
 */
export function flattenArray<T>(arr: any[], depth = 1): T[] {
  return arr.flat(depth)
}

/**
 * 查找数组中符合条件的所有项的索引
 * @param arr 要查找的数组
 * @param predicate 查找条件
 */
export function findAllIndexes<T>(arr: T[], predicate: (item: T) => boolean): number[] {
  return arr.reduce((indexes, item, index) => {
    if (predicate(item)) {
      indexes.push(index)
    }
    return indexes
  }, [] as number[])
}

/**
 * 数组排序
 * @param arr 要排序的数组
 * @param key 如果是对象数组，指定排序的key
 * @param order 排序方式：asc升序，desc降序
 */
export function sortArray<T>(arr: T[], key?: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
  const sorted = [...arr]
  sorted.sort((a, b) => {
    const valueA = key ? a[key] : a
    const valueB = key ? b[key] : b
    if (valueA < valueB) return order === 'asc' ? -1 : 1
    if (valueA > valueB) return order === 'asc' ? 1 : -1
    return 0
  })
  return sorted
}

/**
 * 数组交集
 * @param arr1 数组1
 * @param arr2 数组2
 * @param key 如果是对象数组，指定用于比较的key
 */
export function intersection<T>(arr1: T[], arr2: T[], key?: keyof T): T[] {
  if (key) {
    const set2 = new Set(arr2.map(item => item[key]))
    return arr1.filter(item => set2.has(item[key]))
  }
  return arr1.filter(item => arr2.includes(item))
}

/**
 * 数组差集
 * @param arr1 数组1
 * @param arr2 数组2
 * @param key 如果是对象数组，指定用于比较的key
 */
export function difference<T>(arr1: T[], arr2: T[], key?: keyof T): T[] {
  if (key) {
    const set2 = new Set(arr2.map(item => item[key]))
    return arr1.filter(item => !set2.has(item[key]))
  }
  return arr1.filter(item => !arr2.includes(item))
}

/**
 * 数组转树形结构
 * @param arr 要转换的数组
 * @param id ID字段名
 * @param parentId 父ID字段名
 * @param children 子节点字段名
 */
export function arrayToTree<T extends Record<string, any>>(
  arr: T[],
  id: string = 'id',
  parentId: string = 'parentId',
  children: string = 'children'
): T[] {
  const map = new Map<any, T>()
  const result: T[] = []

  // 先把数组转成Map，方便查找
  arr.forEach(item => map.set(item[id], { ...item, [children]: [] }))

  // 构建树形结构
  map.forEach(item => {
    const parent = map.get(item[parentId])
    if (parent) {
      parent[children].push(item)
    } else {
      result.push(item)
    }
  })

  return result
}

/**
 * 树形结构转数组
 * @param tree 树形结构
 * @param children 子节点字段名
 */
export function treeToArray<T extends Record<string, any>>(
  tree: T[],
  children: string = 'children'
): T[] {
  const result: T[] = []
  const stack = [...tree]

  while (stack.length) {
    const node = stack.pop()!
    const childNodes = node[children]
    
    result.push({ ...node, [children]: undefined })
    
    if (Array.isArray(childNodes) && childNodes.length) {
      stack.push(...childNodes)
    }
  }

  return result
} 