<template>
  <div class="not-found">
    <div class="content">
      <img src="@/assets/images/404.png" alt="404" class="error-image" />
      <h1>404</h1>
      <h2>抱歉，您访问的页面不存在</h2>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style lang="less" scoped>
.not-found {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7f9;

  .content {
    text-align: center;

    .error-image {
      width: 300px;
      margin-bottom: 20px;
    }

    h1 {
      font-size: 72px;
      color: #409eff;
      margin: 0;
    }

    h2 {
      font-size: 24px;
      color: #606266;
      margin: 20px 0;
      font-weight: normal;
    }
  }
}
</style> 