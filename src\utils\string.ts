/**
 * 字符串首字母大写
 * @param str 字符串
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * 驼峰转连字符
 * @param str 字符串
 */
export function camelToKebab(str: string): string {
  return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()
}

/**
 * 连字符转驼峰
 * @param str 字符串
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 截取字符串
 * @param str 字符串
 * @param length 长度
 * @param suffix 后缀
 */
export function truncate(str: string, length: number, suffix: string = '...'): string {
  if (str.length <= length) return str
  return str.slice(0, length) + suffix
}

/**
 * 生成随机字符串
 * @param length 长度
 * @param chars 字符集
 */
export function randomString(
  length: number,
  chars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 格式化手机号
 * @param phone 手机号
 * @param separator 分隔符
 */
export function formatPhone(phone: string, separator: string = ' '): string {
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, `$1${separator}$2${separator}$3`)
}

/**
 * 隐藏手机号中间四位
 * @param phone 手机号
 */
export function hidePhone(phone: string): string {
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 格式化银行卡号
 * @param card 银行卡号
 * @param separator 分隔符
 */
export function formatBankCard(card: string, separator: string = ' '): string {
  return card.replace(/(\d{4})/g, `$1${separator}`).trim()
}

/**
 * 隐藏银行卡号中间位数
 * @param card 银行卡号
 */
export function hideBankCard(card: string): string {
  return card.replace(/^(\d{4})\d+(\d{4})$/, '$1 **** **** $2')
}

/**
 * 格式化身份证号
 * @param idCard 身份证号
 * @param separator 分隔符
 */
export function formatIdCard(idCard: string, separator: string = ' '): string {
  return idCard
    .replace(/(\d{6})(\d{8})(\d{4})/, `$1${separator}$2${separator}$3`)
}

/**
 * 隐藏身份证号中间位数
 * @param idCard 身份证号
 */
export function hideIdCard(idCard: string): string {
  return idCard.replace(/^(\d{6})\d+(\d{4})$/, '$1********$2')
}

/**
 * 检查字符串是否为有效的URL
 * @param str 字符串
 */
export function isUrl(str: string): boolean {
  try {
    new URL(str)
    return true
  } catch {
    return false
  }
}

/**
 * 检查字符串是否为有效的邮箱
 * @param str 字符串
 */
export function isEmail(str: string): boolean {
  return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(str)
}

/**
 * 检查字符串是否为有效的手机号
 * @param str 字符串
 */
export function isPhone(str: string): boolean {
  return /^1[3-9]\d{9}$/.test(str)
}

/**
 * 检查字符串是否为有效的身份证号
 * @param str 字符串
 */
export function isIdCard(str: string): boolean {
  return /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(str)
}

/**
 * 字符串转义HTML特殊字符
 * @param str 字符串
 */
export function escapeHtml(str: string): string {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  }
  return str.replace(/[&<>"']/g, m => map[m])
}

/**
 * HTML特殊字符反转义
 * @param str 字符串
 */
export function unescapeHtml(str: string): string {
  const map: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#039;': "'"
  }
  return str.replace(/&amp;|&lt;|&gt;|&quot;|&#039;/g, m => map[m])
}

/**
 * 移除字符串中的HTML标签
 * @param str 字符串
 */
export function stripHtml(str: string): string {
  return str.replace(/<[^>]+>/g, '')
}

/**
 * 计算字符串的字节长度（中文算2个字节）
 * @param str 字符串
 */
export function getByteLength(str: string): number {
  return str.split('').reduce((len, char) => {
    return len + (char.charCodeAt(0) > 255 ? 2 : 1)
  }, 0)
}

/**
 * 字符串转拼音
 * @param str 字符串
 * @param separator 分隔符
 */
export function toPinyin(str: string): string {
  // 这里需要引入完整的拼音库才能实现
  // 可以使用 pinyin 库: https://github.com/hotoo/pinyin
  // 示例实现仅返回原字符串
  return str
} 