/**
 * @description 判断是否为外部链接
 * @param {string} path
 * @returns {boolean}
 */
export const isExternal = (path: string): boolean => {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @description 判断是否为数组
 * @param {any} arg
 * @returns {boolean}
 */
export const isArray = (arg: any): boolean => {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/**
 * @description 判断是否为字符串
 * @param {any} str
 * @returns {boolean}
 */
export const isString = (str: any): boolean => {
  return typeof str === 'string' || str instanceof String
}

/**
 * @description 判断是否为数字
 * @param {any} num
 * @returns {boolean}
 */
export const isNumber = (num: any): boolean => {
  return typeof num === 'number' || num instanceof Number
}

/**
 * @description 判断是否为布尔值
 * @param {any} bool
 * @returns {boolean}
 */
export const isBoolean = (bool: any): boolean => {
  return typeof bool === 'boolean'
}

/**
 * @description 判断是否为对象
 * @param {any} obj
 * @returns {boolean}
 */
export const isObject = (obj: any): boolean => {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

/**
 * @description 判断是否为空
 * @param {any} val
 * @returns {boolean}
 */
export const isEmpty = (val: any): boolean => {
  if (isArray(val) || isString(val)) {
    return val.length === 0
  }

  if (val instanceof Map || val instanceof Set) {
    return val.size === 0
  }

  if (isObject(val)) {
    return Object.keys(val).length === 0
  }

  return false
}

/**
 * @description 判断是否为手机号
 * @param {string} val
 * @returns {boolean}
 */
export const isMobile = (val: string): boolean => {
  return /^1[3-9]\d{9}$/.test(val)
}

/**
 * @description 判断是否为邮箱
 * @param {string} val
 * @returns {boolean}
 */
export const isEmail = (val: string): boolean => {
  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(val)
}

/**
 * @description 判断是否为身份证号
 * @param {string} val
 * @returns {boolean}
 */
export const isIdCard = (val: string): boolean => {
  return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(val)
}

/**
 * @description 判断是否为URL
 * @param {string} url
 * @returns {boolean}
 */
export const isURL = (url: string): boolean => {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
} 