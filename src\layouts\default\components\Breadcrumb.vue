<template>
  <el-breadcrumb class="breadcrumb">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
        <span
          class="breadcrumb-item"
          :class="{ 'no-redirect': item.redirect === 'noredirect' || index === breadcrumbs.length - 1 }"
          @click="handleLink(item)"
        >
          <el-icon v-if="item.meta?.icon" class="breadcrumb-icon">
            <component :is="item.meta.icon" />
          </el-icon>
          {{ item.meta?.title }}
        </span>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter, type RouteLocationMatched } from 'vue-router'

const route = useRoute()
const router = useRouter()

const breadcrumbs = ref<RouteLocationMatched[]>([])

const getBreadcrumb = () => {
  let matched = route.matched.filter(
    item => item.meta && item.meta.title && item.meta.breadcrumb !== false
  )
  
  // 如果第一个不是首页，添加首页
  const first = matched[0]
  if (first && first.path !== '/dashboard') {
    matched = [
      {
        path: '/dashboard',
        meta: { title: '首页', icon: 'dashboard' }
      } as RouteLocationMatched
    ].concat(matched)
  }
  
  breadcrumbs.value = matched
}

const handleLink = (item: RouteLocationMatched) => {
  const { redirect, path } = item
  if (redirect) {
    router.push(redirect.toString())
    return
  }
  router.push(path)
}

watch(
  () => route.path,
  () => getBreadcrumb(),
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.breadcrumb {
  display: inline-block;
  line-height: 50px;

  :deep(.el-breadcrumb__inner) {
    color: #666;
    cursor: text;

    &.is-link {
      color: #666;
      cursor: pointer;
      
      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  .breadcrumb-item {
    display: inline-flex;
    align-items: center;
    cursor: pointer;

    &.no-redirect {
      cursor: text;
      color: #97a8be;
    }
  }

  .breadcrumb-icon {
    margin-right: 5px;
    font-size: 14px;
  }
}

.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-leave-active {
  position: absolute;
}
</style> 