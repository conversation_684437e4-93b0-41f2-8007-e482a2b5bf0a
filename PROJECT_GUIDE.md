# SAP 后台管理系统项目说明文档

## 项目简介
SAP 后台管理系统是一个基于 Vue 3 + TypeScript + Vite 构建的现代化管理系统框架。项目采用薰衣草紫色主题设计，提供了丰富的后台管理功能，包括数据监控、权限管理、系统设置等模块。

## 技术栈
- 前端框架：Vue 3
- 开发语言：TypeScript
- 构建工具：Vite
- UI 框架：Element Plus
- 状态管理：Pinia
- 路由管理：Vue Router
- HTTP 客户端：Axios
- CSS 预处理器：Less

## 项目结构说明

```
sap-frontend/
├── src/                    # 源代码目录
│   ├── api/               # API 接口定义
│   ├── assets/            # 静态资源文件
│   │   ├── images/       # 图片资源
│   │   └── svg/          # SVG 图标
│   ├── components/        # 公共组件
│   ├── directives/        # 自定义指令
│   ├── hooks/            # 组合式函数
│   ├── layouts/          # 布局组件
│   ├── router/           # 路由配置
│   ├── stores/           # 状态管理
│   ├── styles/           # 全局样式
│   ├── types/            # TypeScript 类型定义
│   ├── utils/            # 工具函数
│   └── views/            # 页面组件
├── public/                # 静态公共资源
├── .husky/               # Git hooks配置
├── .vscode/              # VS Code 编辑器配置
└── types/                # 全局类型声明
```

## 根目录文件说明

### 1. 配置文件
- `vite.config.ts`: Vite 构建工具配置文件，包含插件配置、构建选项等
- `tsconfig.json`: TypeScript 主配置文件，定义编译选项和项目结构
- `tsconfig.app.json`: 应用特定的 TypeScript 配置
- `tsconfig.node.json`: Node.js 环境的 TypeScript 配置
- `.gitignore`: Git 忽略文件配置
- `package.json`: 项目依赖和脚本配置
- `pnpm-lock.yaml`: pnpm 包管理器的依赖锁定文件
- `index.html`: 应用入口 HTML 文件
- `auto-imports.d.ts`: 自动导入的类型声明文件
- `components.d.ts`: 组件的类型声明文件

### 2. 文档文件
- `README.md`: 项目主要说明文档
- `UI_SPECIFICATION.md`: UI 设计规范文档
- `PROJECT_GUIDE.md`: 项目开发指南文档

### 3. 目录说明

#### public/
静态资源目录，包含：
- 网站图标
- 静态图片资源
- 不需要通过构建工具处理的文件

#### .husky/
Git hooks 配置目录，用于：
- 代码提交前的格式化
- 提交信息规范检查
- 自动化测试运行

#### .vscode/
VS Code 编辑器配置目录，包含：
- 编辑器设置
- 代码片段
- 推荐扩展
- 调试配置

## 目录详细说明

### 1. src/api
API 接口管理目录，按模块划分不同的接口文件：
- `user.ts`: 用户相关接口
- 其他业务模块接口

### 2. src/assets
静态资源目录：
- `images/`: 项目图片资源
- `svg/`: SVG 图标资源

### 3. src/components
公共组件目录：
- `AnimatedBackground.vue`: 动画背景组件
- `RoundedDialog.vue`: 圆角对话框组件
- `ParticleBackground.vue`: 粒子背景组件
- 其他公共组件

### 4. src/directives
自定义指令目录：
- `copy.ts`: 复制指令
- `debounce.ts`: 防抖指令
- `throttle.ts`: 节流指令
- `permission.ts`: 权限指令
- `loading.ts`: 加载指令

### 5. src/hooks
可复用的组合式函数：
- `useClipboard.ts`: 剪贴板操作
- `useDialog.ts`: 对话框控制
- `useDownload.ts`: 文件下载
- `useTable.ts`: 表格相关逻辑
- `useForm.ts`: 表单相关逻辑

### 6. src/layouts
布局相关组件：
- `default/`: 默认布局
  - `components/`: 布局子组件
  - `index.vue`: 布局主文件
- `MainLayout.vue`: 主布局组件

### 7. src/router
路由配置文件：
- `index.ts`: 路由主配置文件

### 8. src/stores
状态管理目录：
- `user.ts`: 用户状态管理

### 9. src/styles
全局样式文件：
- `index.less`: 主样式文件
- `components.less`: 组件样式
- `dialog.less`: 对话框样式
- `tooltip.less`: 文字提示样式

### 10. src/views
页面组件目录：
- `access/`: 权限管理相关页面
- `auth/`: 认证相关页面
- `dashboard/`: 仪表盘页面
- `data/`: 数据管理页面
- `monitor/`: 监控相关页面
- `system/`: 系统设置页面

## 开发指南

### 1. 环境要求
- Node.js >= 16.0.0
- pnpm >= 7.0.0

### 2. 开发设置
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

### 3. 代码规范
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 遵循 TypeScript 类型检查

### 4. 组件开发规范
1. 组件命名采用 PascalCase
2. 文件名与组件名保持一致
3. 使用 `<script setup>` 语法
4. 属性和事件使用 kebab-case

### 5. 样式开发规范
1. 使用 Less 预处理器
2. 遵循 BEM 命名规范
3. 优先使用项目预设的变量
4. 组件样式使用 scoped 属性

### 6. Git 提交规范
提交信息格式：
```
type(scope): subject

body

footer
```

类型（type）：
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建

## 部署指南

### 1. 构建
```bash
pnpm build
```

### 2. 部署配置
- 配置 nginx 反向代理
- 设置环境变量
- 配置 SSL 证书

### 3. 环境变量配置
在项目根目录创建以下文件：
- `.env`: 默认环境配置
- `.env.development`: 开发环境配置
- `.env.production`: 生产环境配置

## 常见问题

### 1. 开发相关
- 确保 Node.js 版本符合要求
- 检查包管理器版本
- 清除浏览器缓存

### 2. 构建相关
- 检查依赖完整性
- 验证环境变量配置
- 确认构建命令正确

### 3. 部署相关
- 验证服务器配置
- 检查网络连接
- 确认权限设置 