import { ref, computed } from 'vue'
import type { Component } from 'vue'

type ColumnFormatter<T> = (row: T, column: any, cellValue: any, index: number) => string

export interface TableConfig<T = Record<string, any>> {
  // API配置
  api: {
    list: (params: any) => Promise<{
      list: T[]
      total: number
    }>
    delete?: (id: string | number) => Promise<void>
    batchDelete?: (ids: (string | number)[]) => Promise<void>
  }
  // 表格列配置
  columns: {
    prop: keyof T
    label: string
    width?: number | string
    fixed?: boolean | 'left' | 'right'
    sortable?: boolean | 'custom'
    align?: 'left' | 'center' | 'right'
    formatter?: ColumnFormatter<T>
    render?: (row: T) => string | Component
  }[]
  // 查询条件配置
  searchConfig?: {
    fields: {
      prop: keyof T
      label: string
      type: 'input' | 'select' | 'date' | 'daterange'
      options?: { label: string; value: any }[]
    }[]
  }
  // 操作按钮配置
  actionConfig?: {
    width?: number | string
    fixed?: boolean | 'left' | 'right'
    buttons: {
      type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
      label: string
      permission?: string
      onClick: (row: T) => void
    }[]
  }
  // 批量操作配置
  batchConfig?: {
    selection: boolean
    buttons: {
      type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
      label: string
      permission?: string
      onClick: (rows: T[]) => void
    }[]
  }
  // 分页配置
  pagination?: {
    pageSize?: number
    pageSizes?: number[]
    layout?: string
  }
}

export function useTable<T = Record<string, any>>(config: TableConfig<T>) {
  // 表格数据
  const tableData = ref<T[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 分页参数
  const currentPage = ref(1)
  const pageSize = ref(config.pagination?.pageSize || 10)

  // 查询参数
  const searchForm = ref<Partial<T>>({})

  // 选中行
  const selection = ref<T[]>([])

  // 排序参数
  const sortParams = ref({
    prop: '',
    order: ''
  })

  // 计算属性：是否显示分页
  const showPagination = computed(() => {
    return total.value > 0
  })

  // 计算属性：是否显示批量操作按钮
  const showBatchButtons = computed(() => {
    return config.batchConfig?.selection && selection.value.length > 0
  })

  // 获取表格数据
  const getTableData = async () => {
    if (!config.api.list) return

    loading.value = true
    try {
      const params = {
        page: currentPage.value,
        pageSize: pageSize.value,
        ...searchForm.value,
        ...sortParams.value
      }

      const { list, total: totalCount } = await config.api.list(params)
      tableData.value = list
      total.value = totalCount
    } catch (error) {
      console.error('获取表格数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 处理分页变化
  const handlePageChange = (page: number) => {
    currentPage.value = page
    getTableData()
  }

  // 处理每页条数变化
  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    getTableData()
  }

  // 处理排序变化
  const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
    sortParams.value = {
      prop,
      order: order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
    }
    getTableData()
  }

  // 处理搜索
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 处理重置
  const handleReset = () => {
    searchForm.value = {}
    currentPage.value = 1
    sortParams.value = {
      prop: '',
      order: ''
    }
    getTableData()
  }

  // 处理选择变化
  const handleSelectionChange = (rows: T[]) => {
    selection.value = rows
  }

  // 处理删除
  const handleDelete = async (row: T & { id: string | number }) => {
    if (!config.api.delete) return

    try {
      await config.api.delete(row.id)
      getTableData()
    } catch (error) {
      console.error('删除失败:', error)
    }
  }

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (!config.api.batchDelete) return

    try {
      const ids = selection.value.map((item: any) => item.id)
      await config.api.batchDelete(ids)
      getTableData()
    } catch (error) {
      console.error('批量删除失败:', error)
    }
  }

  return {
    // 数据
    tableData,
    loading,
    total,
    currentPage,
    pageSize,
    searchForm,
    selection,
    sortParams,
    showPagination,
    showBatchButtons,

    // 方法
    getTableData,
    handlePageChange,
    handleSizeChange,
    handleSortChange,
    handleSearch,
    handleReset,
    handleSelectionChange,
    handleDelete,
    handleBatchDelete
  }
} 