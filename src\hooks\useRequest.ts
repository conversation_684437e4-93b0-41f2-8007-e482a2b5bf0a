import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import request from '@/utils/request'

export interface RequestConfig<T = any, R = T> extends AxiosRequestConfig {
  // 是否自动显示错误信息
  showError?: boolean
  // 是否自动显示成功信息
  showSuccess?: boolean
  // 成功信息文本
  successMessage?: string
  // 错误信息文本
  errorMessage?: string
  // 是否自动格式化响应数据
  formatData?: boolean
  // 格式化响应数据的函数
  formatter?: ((data: any) => R) | undefined;
  // 请求前的回调
  beforeRequest?: () => void
  // 请求成功的回调
  onSuccess?: (data: R) => void
  // 请求失败的回调
  onError?: (error: Error) => void
  // 请求完成的回调
  onComplete?: () => void
}

export function useRequest<T = any>(defaultConfig: RequestConfig<T> = {}) {
  // 加载状态
  const loading = ref(false)
  // 响应数据
  const data = ref<T>()
  // 错误信息
  const error = ref<Error>()

  // 发送请求
  const send = async <R = T>(config: RequestConfig<T, R>): Promise<R> => {
    // 合并配置
    const finalConfig = {
      ...defaultConfig,
      ...config,
      showError: config.showError ?? defaultConfig.showError ?? true,
      showSuccess: config.showSuccess ?? defaultConfig.showSuccess ?? false,
      formatData: config.formatData ?? defaultConfig.formatData ?? true
    }

    // 重置状态
    loading.value = true
    error.value = undefined

    try {
      // 请求前回调
      finalConfig.beforeRequest?.()

      // 发送请求
      const response: AxiosResponse = await request.request(finalConfig)

      // 格式化数据
      let result = response.data
      if (finalConfig.formatData) {
        if (finalConfig.formatter) {
          result = finalConfig.formatter(result)
        } else if (typeof result === 'object' && 'data' in result) {
          result = result.data
        }
      }

      // 保存数据
      data.value = result as T

      // 显示成功信息
      if (finalConfig.showSuccess && finalConfig.successMessage) {
        ElMessage.success(finalConfig.successMessage)
      }

      // 成功回调
      finalConfig.onSuccess?.(result)

      return result
    } catch (e) {
      // 保存错误
      const err = e instanceof Error ? e : new Error(String(e))
      error.value = err

      // 显示错误信息
      if (finalConfig.showError) {
        ElMessage.error(finalConfig.errorMessage || err.message)
      }

      // 错误回调
      finalConfig.onError?.(err)

      throw err
    } finally {
      loading.value = false
      // 完成回调
      finalConfig.onComplete?.()
    }
  }

  // GET 请求
  const get = <R = T>(url: string, config: Omit<RequestConfig<T, R>, 'url' | 'method'> = {}) => {
    return send<R>({
      ...config,
      method: 'GET',
      url
    })
  }

  // POST 请求
  const post = <R = T>(url: string, data?: any, config: Omit<RequestConfig<T, R>, 'url' | 'method' | 'data'> = {}) => {
    return send<R>({
      ...config,
      method: 'POST',
      url,
      data
    })
  }

  // PUT 请求
  const put = <R = T>(url: string, data?: any, config: Omit<RequestConfig<T, R>, 'url' | 'method' | 'data'> = {}) => {
    return send<R>({
      ...config,
      method: 'PUT',
      url,
      data
    })
  }

  // DELETE 请求
  const del = <R = T>(url: string, config: Omit<RequestConfig<T, R>, 'url' | 'method'> = {}) => {
    return send<R>({
      ...config,
      method: 'DELETE',
      url
    })
  }

  return {
    loading,
    data,
    error,
    send,
    get,
    post,
    put,
    del
  }
} 