import { computed } from 'vue'
import type { WritableComputedRef } from 'vue'

/**
 * 创建一个支持 v-model 的计算属性
 * @param props 组件的 props
 * @param key props 中的属性名
 * @param emit 组件的 emit 函数
 */
export function useVModel<
  P extends Record<string, any>,
  K extends keyof P,
  Name extends string
>(
  props: P,
  key: K,
  emit: (name: Name, ...args: any[]) => void,
  eventName: Name = `update:${String(key)}` as Name
): WritableComputedRef<P[K]> {
  return computed({
    get() {
      return props[key]
    },
    set(value) {
      emit(eventName, value)
    }
  })
}

/**
 * 创建多个支持 v-model 的计算属性
 * @param props 组件的 props
 * @param emit 组件的 emit 函数
 * @param keys 需要支持 v-model 的属性名数组
 */
export function useVModels<
  P extends Record<string, any>,
  K extends keyof P
>(
  props: P,
  emit: (name: string, ...args: any[]) => void,
  keys: K[] = Object.keys(props) as K[]
): { [key in K]: WritableComputedRef<P[key]> } {
  const result = {} as { [key in K]: WritableComputedRef<P[key]> }

  keys.forEach(key => {
    result[key] = computed({
      get() {
        return props[key]
      },
      set(value) {
        emit(`update:${String(key)}`, value)
      }
    })
  })

  return result
} 