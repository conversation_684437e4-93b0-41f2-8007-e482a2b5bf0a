import dayjs from 'dayjs'
import type { OpUnitType, QUnitType } from 'dayjs';

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式化模板
 */
export function formatDate(date: Date | string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  return dayjs(date).format(format)
}

/**
 * 获取相对时间
 * @param date 日期
 * @param now 当前时间，默认为现在
 */
export function getRelativeTime(date: Date | string | number, now: Date = new Date()): string {
  const diff = dayjs(now).diff(date, 'second')
  
  if (diff < 60) return '刚刚'
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`
  if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`
  if (diff < 2592000) return `${Math.floor(diff / 86400)}天前`
  if (diff < 31536000) return `${Math.floor(diff / 2592000)}个月前`
  return `${Math.floor(diff / 31536000)}年前`
}

/**
 * 获取日期范围
 * @param type 范围类型：today, week, month, year
 */
export function getDateRange(type: 'today' | 'week' | 'month' | 'year'): [Date, Date] {
  const now = dayjs()
  
  switch (type) {
    case 'today':
      return [
        now.startOf('day').toDate(),
        now.endOf('day').toDate()
      ]
    case 'week':
      return [
        now.startOf('week').toDate(),
        now.endOf('week').toDate()
      ]
    case 'month':
      return [
        now.startOf('month').toDate(),
        now.endOf('month').toDate()
      ]
    case 'year':
      return [
        now.startOf('year').toDate(),
        now.endOf('year').toDate()
      ]
  }
}

/**
 * 添加时间
 * @param date 日期
 * @param amount 数量
 * @param unit 单位：year, month, week, day, hour, minute, second
 */
export function addTime(
  date: Date | string | number,
  amount: number,
  unit: 'year' | 'month' | 'week' | 'day' | 'hour' | 'minute' | 'second'
): Date {
  return dayjs(date).add(amount, unit).toDate()
}

/**
 * 减少时间
 * @param date 日期
 * @param amount 数量
 * @param unit 单位：year, month, week, day, hour, minute, second
 */
export function subtractTime(
  date: Date | string | number,
  amount: number,
  unit: 'year' | 'month' | 'week' | 'day' | 'hour' | 'minute' | 'second'
): Date {
  return dayjs(date).subtract(amount, unit).toDate()
}

/**
 * 比较两个日期
 * @param date1 日期1
 * @param date2 日期2
 * @param unit 比较单位：year, month, week, day, hour, minute, second
 */
export function compareDates(
  date1: Date | string | number,
  date2: Date | string | number,
  unit: 'year' | 'month' | 'week' | 'day' | 'hour' | 'minute' | 'second'
): number {
  return dayjs(date1).diff(date2, unit)
}

/**
 * 检查日期是否在范围内
 * @param date 要检查的日期
 * @param start 开始日期
 * @param end 结束日期
 */
export function isDateInRange(
  date: Date | string | number,
  start: Date | string | number,
  end: Date | string | number
): boolean {
  const d = dayjs(date)
  return d.isAfter(start) && d.isBefore(end)
}

/**
 * 获取月份的天数
 * @param year 年份
 * @param month 月份（1-12）
 */
export function getDaysInMonth(year: number, month: number): number {
  return dayjs(`${year}-${month}`).daysInMonth()
}

/**
 * 检查是否为工作日
 * @param date 日期
 */
export function isWeekday(date: Date | string | number): boolean {
  const day = dayjs(date).day()
  return day !== 0 && day !== 6
}

/**
 * 获取季度的起始和结束日期
 * @param date 日期
 */
export function getQuarterRange(date: Date | string | number): [Date, Date] {
  const d = dayjs(date)
  return [
    d.startOf('quarter' as OpUnitType).toDate(),
    d.endOf('quarter' as OpUnitType).toDate()
  ]
}

/**
 * 格式化时间段
 * @param seconds 秒数
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  const parts = []
  if (hours > 0) parts.push(`${hours}小时`)
  if (minutes > 0) parts.push(`${minutes}分钟`)
  if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`)

  return parts.join('')
}
