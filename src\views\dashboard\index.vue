<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>访问量</span>
              <el-tag type="success" size="small">日</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="card-number">1234</div>
            <div class="card-chart">
              <el-progress :percentage="80" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>销售额</span>
              <el-tag type="warning" size="small">月</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="card-number">¥ 56,789</div>
            <div class="card-chart">
              <el-progress :percentage="65" type="warning" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>订单数</span>
              <el-tag type="danger" size="small">周</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="card-number">789</div>
            <div class="card-chart">
              <el-progress :percentage="45" type="danger" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>用户数</span>
              <el-tag type="info" size="small">总</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="card-number">12,345</div>
            <div class="card-chart">
              <el-progress :percentage="90" type="info" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <el-col :span="16">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
            </div>
          </template>
          <div ref="chartRef" style="height: 300px"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>最新动态</span>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :timestamp="activity.timestamp"
              :type="activity.type"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()

const activities = [
  {
    content: '完成了系统升级',
    timestamp: '2024-03-15 19:00',
    type: 'success'
  },
  {
    content: '新增用户 10 人',
    timestamp: '2024-03-15 17:30',
    type: 'primary'
  },
  {
    content: '发布新版本 v1.0.1',
    timestamp: '2024-03-15 15:00',
    type: 'warning'
  },
  {
    content: '系统维护通知',
    timestamp: '2024-03-15 12:00',
    type: 'info'
  }
]

onMounted(() => {
  if (chartRef.value) {
    const chart = echarts.init(chartRef.value)
    chart.setOption({
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['访问量', '销售额']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '访问量',
          type: 'line',
          data: [120, 132, 101, 134, 90, 230, 210]
        },
        {
          name: '销售额',
          type: 'line',
          data: [220, 182, 191, 234, 290, 330, 310]
        }
      ]
    })

    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
})
</script>

<style lang="less" scoped>
.dashboard {
  padding: 20px;

  &-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      text-align: center;

      .card-number {
        font-size: 24px;
        font-weight: bold;
        margin: 10px 0;
      }

      .card-chart {
        margin-top: 20px;
      }
    }
  }
}
</style> 