// 引入圆润组件样式
@import './dialog.less';
@import './components.less';
@import './tooltip.less';

// 全局变量 - 薰衣草紫色主题风格
:root {
  // 主题色 - 薰衣草紫色系
  --primary-color: #9370db;
  --primary-light: #ba85d6;
  --primary-dark: #7b68ee;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;

  // 文字颜色 - 更现代化的色彩
  --text-color-primary: #1a1f36;
  --text-color-regular: #697386;
  --text-color-secondary: #97a3b9;
  --text-color-placeholder: #c0c4cc;

  // 边框颜色 - 更柔和的边框
  --border-color-base: #e5e7eb;
  --border-color-light: #f3f4f6;
  --border-color-lighter: #f9fafb;
  --border-color-extra-light: #f2f6fc;

  // 背景颜色
  --background-color-base: #f5f7fd;

  // 圆角变量
  --border-radius-base: 12px;
  --border-radius-small: 8px;
  --border-radius-large: 16px;
  --border-radius-round: 24px;
}

// 重置样式
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color-primary);
  background-color: var(--background-color-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

// 文字溢出省略号
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// flex 布局
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 间距
.m {
  &-1 {
    margin: 4px;
  }
  &-2 {
    margin: 8px;
  }
  &-3 {
    margin: 12px;
  }
  &-4 {
    margin: 16px;
  }
  &-5 {
    margin: 20px;
  }
}

.mt {
  &-1 {
    margin-top: 4px;
  }
  &-2 {
    margin-top: 8px;
  }
  &-3 {
    margin-top: 12px;
  }
  &-4 {
    margin-top: 16px;
  }
  &-5 {
    margin-top: 20px;
  }
}

.mr {
  &-1 {
    margin-right: 4px;
  }
  &-2 {
    margin-right: 8px;
  }
  &-3 {
    margin-right: 12px;
  }
  &-4 {
    margin-right: 16px;
  }
  &-5 {
    margin-right: 20px;
  }
}

.mb {
  &-1 {
    margin-bottom: 4px;
  }
  &-2 {
    margin-bottom: 8px;
  }
  &-3 {
    margin-bottom: 12px;
  }
  &-4 {
    margin-bottom: 16px;
  }
  &-5 {
    margin-bottom: 20px;
  }
}

.ml {
  &-1 {
    margin-left: 4px;
  }
  &-2 {
    margin-left: 8px;
  }
  &-3 {
    margin-left: 12px;
  }
  &-4 {
    margin-left: 16px;
  }
  &-5 {
    margin-left: 20px;
  }
}

.p {
  &-1 {
    padding: 4px;
  }
  &-2 {
    padding: 8px;
  }
  &-3 {
    padding: 12px;
  }
  &-4 {
    padding: 16px;
  }
  &-5 {
    padding: 20px;
  }
}

.pt {
  &-1 {
    padding-top: 4px;
  }
  &-2 {
    padding-top: 8px;
  }
  &-3 {
    padding-top: 12px;
  }
  &-4 {
    padding-top: 16px;
  }
  &-5 {
    padding-top: 20px;
  }
}

.pr {
  &-1 {
    padding-right: 4px;
  }
  &-2 {
    padding-right: 8px;
  }
  &-3 {
    padding-right: 12px;
  }
  &-4 {
    padding-right: 16px;
  }
  &-5 {
    padding-right: 20px;
  }
}

.pb {
  &-1 {
    padding-bottom: 4px;
  }
  &-2 {
    padding-bottom: 8px;
  }
  &-3 {
    padding-bottom: 12px;
  }
  &-4 {
    padding-bottom: 16px;
  }
  &-5 {
    padding-bottom: 20px;
  }
}

.pl {
  &-1 {
    padding-left: 4px;
  }
  &-2 {
    padding-left: 8px;
  }
  &-3 {
    padding-left: 12px;
  }
  &-4 {
    padding-left: 16px;
  }
  &-5 {
    padding-left: 20px;
  }
} 