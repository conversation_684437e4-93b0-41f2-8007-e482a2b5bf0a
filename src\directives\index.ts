import type { App } from 'vue'
import { vLoading } from './loading'
import { vPermission } from './permission'
import { vDebounce } from './debounce'
import { vThrottle } from './throttle'
import { vCopy } from './copy'

export default {
  install(app: App) {
    app.directive('loading', vLoading)
    app.directive('permission', vPermission)
    app.directive('debounce', vDebounce)
    app.directive('throttle', vThrottle)
    app.directive('copy', vCopy)
  }
} 