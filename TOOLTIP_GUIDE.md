# 圆润 Tooltip 使用指南

## 概述

本项目已经优化了所有 Tooltip 组件的样式，使其更加圆润并符合整体设计风格。特别是左侧菜单收起时的悬浮提示，现在具有更好的视觉效果。

## 设计特点

### 🎨 视觉风格
- **圆润边角**：16px 圆角半径，营造温和友好的感觉
- **毛玻璃效果**：`backdrop-filter: blur(20px)` 提供现代感
- **渐变边框**：微妙的渐变边框增强视觉层次
- **阴影效果**：多层阴影营造浮动感

### ✨ 交互动画
- **平滑进入**：从下方滑入并伴随缩放效果
- **悬停反馈**：鼠标悬停时轻微放大和阴影增强
- **优雅退出**：快速淡出避免视觉干扰

## 使用方法

### 1. 基础圆润 Tooltip

```vue
<template>
  <el-tooltip
    effect="light"
    content="这是一个圆润的提示"
    placement="right"
    :popper-class="'rounded-menu-tooltip'"
    :show-after="300"
    :hide-after="100"
  >
    <el-button>悬停查看提示</el-button>
  </el-tooltip>
</template>
```

### 2. 菜单收起时的 Tooltip

菜单收起时的 tooltip 已经自动应用圆润样式：

```vue
<template>
  <el-tooltip
    v-if="isCollapsed"
    effect="light"
    :content="menuTitle"
    placement="right"
    :popper-class="'rounded-menu-tooltip'"
    :popper-options="{
      modifiers: [{
        name: 'offset',
        options: { offset: [0, 12] }
      },{
        name: 'preventOverflow',
        options: { padding: 20 }
      }]
    }"
    :show-after="300"
    :hide-after="100"
    :enterable="false"
    transition="tooltip-fade"
  >
    <!-- 菜单项内容 -->
  </el-tooltip>
</template>
```

### 3. 不同尺寸的 Tooltip

```vue
<template>
  <!-- 紧凑型 -->
  <el-tooltip
    effect="light"
    content="紧凑提示"
    :popper-class="'rounded-menu-tooltip compact-tooltip'"
  >
    <el-button size="small">紧凑</el-button>
  </el-tooltip>

  <!-- 大型 -->
  <el-tooltip
    effect="light"
    content="这是一个较大的提示信息，可以包含更多内容"
    :popper-class="'rounded-menu-tooltip large-tooltip'"
  >
    <el-button size="large">大型</el-button>
  </el-tooltip>
</template>
```

### 4. 不同状态的 Tooltip

```vue
<template>
  <!-- 成功状态 -->
  <el-tooltip
    effect="light"
    content="操作成功"
    :popper-class="'rounded-menu-tooltip success-tooltip'"
  >
    <el-button type="success">成功</el-button>
  </el-tooltip>

  <!-- 警告状态 -->
  <el-tooltip
    effect="light"
    content="请注意"
    :popper-class="'rounded-menu-tooltip warning-tooltip'"
  >
    <el-button type="warning">警告</el-button>
  </el-tooltip>

  <!-- 错误状态 -->
  <el-tooltip
    effect="light"
    content="操作失败"
    :popper-class="'rounded-menu-tooltip error-tooltip'"
  >
    <el-button type="danger">错误</el-button>
  </el-tooltip>

  <!-- 信息状态 -->
  <el-tooltip
    effect="light"
    content="提示信息"
    :popper-class="'rounded-menu-tooltip info-tooltip'"
  >
    <el-button type="info">信息</el-button>
  </el-tooltip>
</template>
```

## 样式类说明

### 基础样式类
- `rounded-menu-tooltip` - 基础圆润样式
- `compact-tooltip` - 紧凑型样式
- `large-tooltip` - 大型样式

### 状态样式类
- `success-tooltip` - 成功状态（绿色主题）
- `warning-tooltip` - 警告状态（橙色主题）
- `error-tooltip` - 错误状态（红色主题）
- `info-tooltip` - 信息状态（蓝色主题）

## 配置参数

### 推荐配置
```javascript
const tooltipConfig = {
  effect: 'light',                    // 使用浅色主题
  showAfter: 300,                     // 300ms 后显示
  hideAfter: 100,                     // 100ms 后隐藏
  enterable: false,                   // 不允许鼠标进入
  transition: 'tooltip-fade',         // 使用自定义过渡动画
  popperClass: 'rounded-menu-tooltip' // 应用圆润样式
}
```

### Popper 选项
```javascript
const popperOptions = {
  modifiers: [{
    name: 'offset',
    options: { offset: [0, 12] }       // 12px 偏移距离
  },{
    name: 'preventOverflow',
    options: { padding: 20 }           // 20px 边距防止溢出
  }]
}
```

## 最佳实践

### 1. 一致性
- 在整个应用中使用相同的 tooltip 样式
- 保持相同的显示/隐藏时间
- 使用统一的偏移距离

### 2. 可访问性
- 确保 tooltip 内容简洁明了
- 避免在 tooltip 中放置重要的交互元素
- 支持键盘导航

### 3. 性能优化
- 合理设置 `showAfter` 时间避免频繁触发
- 在移动设备上考虑禁用 tooltip
- 使用 `enterable: false` 避免意外交互

### 4. 响应式设计
- 在小屏幕设备上使用更紧凑的样式
- 考虑不同屏幕密度的显示效果
- 支持暗色主题和高对比度模式

## 注意事项

1. **浏览器兼容性**：毛玻璃效果在某些旧版浏览器中可能不支持
2. **性能考虑**：过多的模糊效果可能影响性能
3. **移动端适配**：在触摸设备上 tooltip 的行为可能不同
4. **内容长度**：避免 tooltip 内容过长影响用户体验

## 自定义样式

如果需要创建自定义的 tooltip 样式，可以参考以下模板：

```less
.my-custom-tooltip {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(105, 65, 198, 0.15) !important;
  border-radius: 16px !important;
  box-shadow: 
    0 12px 32px rgba(105, 65, 198, 0.15), 
    0 4px 16px rgba(0, 0, 0, 0.08) !important;
  backdrop-filter: blur(20px) !important;
  padding: 12px 16px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  color: #1a1f36 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  
  // 添加自定义效果
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, 
      rgba(105, 65, 198, 0.2) 0%, 
      rgba(159, 122, 234, 0.1) 50%,
      rgba(105, 65, 198, 0.2) 100%);
    border-radius: 16px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
}
```

## 更新日志

- v1.0.0: 初始版本，基础圆润 tooltip 样式
- v1.1.0: 添加菜单收起时的 tooltip 优化
- v1.2.0: 增加多种状态和尺寸的 tooltip 样式
- v1.3.0: 完善响应式设计和可访问性支持
