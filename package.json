{"name": "sap-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.6.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@types/node": "^20.19.9", "@vitejs/plugin-vue": "^5.0.4", "@vueuse/core": "^13.5.0", "dayjs": "^1.11.13", "less": "^4.2.0", "typescript": "^5.2.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^5.4.19", "vue-tsc": "^1.8.27"}}