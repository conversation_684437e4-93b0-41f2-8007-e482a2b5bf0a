// 全局组件圆润样式 - 参考登录页面设计风格

// 按钮组件样式优化
.el-button {
  border-radius: var(--border-radius-base) !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  
  // 主要按钮
  &--primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #7b68ee 100%) !important;
    border: none !important;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transform: translateX(-100%);
      transition: transform 0.5s ease;
    }
    
    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 8px 16px rgba(147, 112, 219, 0.3) !important;
      
      &::before {
        transform: translateX(100%);
      }
    }
    
    &:active {
      transform: translateY(0) !important;
    }
  }
  
  // 默认按钮
  &--default {
    background: rgba(249, 250, 251, 0.8) !important;
    border: 1px solid var(--border-color-base) !important;
    color: var(--text-color-primary) !important;
    
    &:hover {
      background: rgba(255, 255, 255, 1) !important;
      border-color: var(--primary-color) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(147, 112, 219, 0.15) !important;
    }
  }
}

// 输入框组件样式优化
.el-input {
  .el-input__wrapper {
    border-radius: var(--border-radius-base) !important;
    background-color: rgba(249, 250, 251, 0.8) !important;
    border: 1px solid var(--border-color-base) !important;
    transition: all 0.3s ease !important;
    
    &:hover {
      border-color: var(--primary-color) !important;
      transform: translateY(-1px) !important;
    }
    
    &.is-focus {
      background-color: white !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 4px rgba(66, 99, 235, 0.1) !important;
      transform: translateY(-2px) !important;
    }
  }
  
  .el-input__inner {
    color: var(--text-color-primary) !important;
    
    &::placeholder {
      color: var(--text-color-secondary) !important;
    }
  }
}

// 选择器组件样式优化
.el-select {
  .el-select__wrapper {
    border-radius: var(--border-radius-base) !important;
    background-color: rgba(249, 250, 251, 0.8) !important;
    border: 1px solid var(--border-color-base) !important;
    transition: all 0.3s ease !important;
    
    &:hover {
      border-color: var(--primary-color) !important;
      transform: translateY(-1px) !important;
    }
    
    &.is-focused {
      background-color: white !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 4px rgba(66, 99, 235, 0.1) !important;
      transform: translateY(-2px) !important;
    }
  }
}

// 下拉菜单样式优化
.el-popper {
  &.el-select__popper {
    border-radius: var(--border-radius-large) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(66, 99, 235, 0.15) !important;
    box-shadow: 0 12px 32px rgba(66, 99, 235, 0.15), 
                0 4px 16px rgba(0, 0, 0, 0.08) !important;
    
    .el-select-dropdown__item {
      border-radius: var(--border-radius-small) !important;
      margin: 2px 8px !important;
      transition: all 0.2s ease !important;
      
      &:hover {
        background: rgba(66, 99, 235, 0.08) !important;
        color: var(--primary-color) !important;
      }
      
      &.is-selected {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
        color: white !important;
      }
    }
  }
}

// 表格组件样式优化
.el-table {
  border-radius: var(--border-radius-large) !important;
  overflow: hidden !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--border-color-light) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04) !important;
  
  .el-table__header {
    background: linear-gradient(135deg, 
      rgba(66, 99, 235, 0.05) 0%, 
      rgba(142, 152, 245, 0.03) 100%) !important;
    
    th {
      background: transparent !important;
      border-bottom: 1px solid var(--border-color-light) !important;
      color: var(--text-color-primary) !important;
      font-weight: 600 !important;
    }
  }
  
  .el-table__body {
    tr {
      transition: all 0.2s ease !important;
      
      &:hover {
        background: rgba(66, 99, 235, 0.03) !important;
      }
    }
    
    td {
      border-bottom: 1px solid var(--border-color-lighter) !important;
    }
  }
}

// 分页组件样式优化
.el-pagination {
  .el-pager li {
    border-radius: var(--border-radius-small) !important;
    margin: 0 2px !important;
    transition: all 0.3s ease !important;
    
    &:hover {
      background: rgba(66, 99, 235, 0.1) !important;
      color: var(--primary-color) !important;
      transform: translateY(-1px) !important;
    }
    
    &.is-active {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
      color: white !important;
    }
  }
  
  .btn-prev,
  .btn-next {
    border-radius: var(--border-radius-small) !important;
    transition: all 0.3s ease !important;
    
    &:hover {
      background: rgba(66, 99, 235, 0.1) !important;
      color: var(--primary-color) !important;
      transform: translateY(-1px) !important;
    }
  }
}

// 卡片组件样式优化
.el-card {
  border-radius: var(--border-radius-large) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--border-color-light) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.3s ease !important;
  
  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(66, 99, 235, 0.1) !important;
  }
  
  .el-card__header {
    background: linear-gradient(135deg, 
      rgba(66, 99, 235, 0.05) 0%, 
      rgba(142, 152, 245, 0.03) 100%) !important;
    border-bottom: 1px solid var(--border-color-light) !important;
    border-radius: var(--border-radius-large) var(--border-radius-large) 0 0 !important;
    
    .el-card__header-title {
      color: var(--text-color-primary) !important;
      font-weight: 600 !important;
    }
  }
}

// 标签页组件样式优化
.el-tabs {
  .el-tabs__header {
    .el-tabs__nav {
      border-radius: var(--border-radius-base) !important;
      background: rgba(249, 250, 251, 0.8) !important;
      padding: 4px !important;
    }
    
    .el-tabs__item {
      border-radius: var(--border-radius-small) !important;
      margin: 0 2px !important;
      transition: all 0.3s ease !important;
      border: none !important;
      
      &:hover {
        background: rgba(66, 99, 235, 0.1) !important;
        color: var(--primary-color) !important;
      }
      
      &.is-active {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
        color: white !important;
      }
    }
  }
}

// 消息提示样式优化
.el-message {
  border-radius: var(--border-radius-large) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid var(--border-color-light) !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
  
  &--success {
    border-color: rgba(103, 194, 58, 0.3) !important;
    background: rgba(103, 194, 58, 0.05) !important;
  }
  
  &--warning {
    border-color: rgba(230, 162, 60, 0.3) !important;
    background: rgba(230, 162, 60, 0.05) !important;
  }
  
  &--error {
    border-color: rgba(245, 108, 108, 0.3) !important;
    background: rgba(245, 108, 108, 0.05) !important;
  }
  
  &--info {
    border-color: rgba(66, 99, 235, 0.3) !important;
    background: rgba(66, 99, 235, 0.05) !important;
  }
}

// 抽屉组件样式优化
.el-drawer {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  
  .el-drawer__header {
    background: linear-gradient(135deg, 
      rgba(66, 99, 235, 0.05) 0%, 
      rgba(142, 152, 245, 0.03) 100%) !important;
    border-bottom: 1px solid var(--border-color-light) !important;
    
    .el-drawer__title {
      color: var(--text-color-primary) !important;
      font-weight: 600 !important;
    }
  }
}
