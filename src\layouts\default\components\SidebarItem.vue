<template>
  <div v-if="!item.meta?.hidden">
    <template v-if="!hasOneShowingChild(item.children, item)">
      <el-sub-menu :index="resolvePath(item.path)">
        <template #title>
          <el-icon v-if="item.meta?.icon">
            <component :is="item.meta.icon" />
          </el-icon>
          <span>{{ item.meta?.title }}</span>
        </template>
        <sidebar-item
          v-for="child in item.children"
          :key="child.path"
          :item="child"
          :base-path="resolvePath(child.path)"
        />
      </el-sub-menu>
    </template>

    <template v-else>
      <el-tooltip
        v-if="isCollapsed"
        effect="light"
        :content="onlyOneChild.meta?.title || '菜单项'"
        placement="right"
        :popper-class="'rounded-tooltip'"
        :popper-options="{
          modifiers: [{
            name: 'offset',
            options: { offset: [0, 12] }
          },{
            name: 'preventOverflow',
            options: { padding: 20 }
          }]
        }"
        :show-after="300"
        :hide-after="100"
        :enterable="false"
        transition="tooltip-fade"
      >
        <el-menu-item :index="resolvePath(onlyOneChild.path)" class="sidebar-tooltip-item">
          <el-icon v-if="onlyOneChild.meta?.icon">
            <component :is="onlyOneChild.meta.icon" />
          </el-icon>
          <template #title>{{ onlyOneChild.meta?.title }}</template>
        </el-menu-item>
      </el-tooltip>
      <el-menu-item
        v-else
        :index="resolvePath(onlyOneChild.path)"
      >
        <el-icon v-if="onlyOneChild.meta?.icon">
          <component :is="onlyOneChild.meta.icon" />
        </el-icon>
        <template #title>{{ onlyOneChild.meta?.title }}</template>
      </el-menu-item>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue'
import { isExternal } from '@/utils/validate'
import type { RouteRecordRaw } from 'vue-router'
import path from 'path-browserify'

const props = defineProps<{
  item: RouteRecordRaw
  basePath: string
}>()

// 注入父组件的 isCollapse 状态
const isCollapsed = inject<boolean>('isCollapse', false)

const onlyOneChild = ref<RouteRecordRaw | null>(null)

const hasOneShowingChild = (children: RouteRecordRaw[] = [], parent: RouteRecordRaw) => {
  const showingChildren = children.filter(item => {
    if (item.meta?.hidden) {
      return false
    } else {
      // 临时设置
      onlyOneChild.value = item
      return true
    }
  })

  // 当只有一个子路由时，默认展示该子路由
  if (showingChildren.length === 1) {
    return true
  }

  // 没有子路由时，显示父路由
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
}

const resolvePath = (routePath: string) => {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  return path.resolve(props.basePath, routePath)
}
</script>

<style lang="less" scoped>
.el-menu-item, :deep(.el-sub-menu__title) {
  .el-icon {
    margin-right: 12px;
    font-size: 16px;
  }
}

// 圆润悬浮提示样式 - 参考登录页面风格
:deep(.rounded-tooltip) {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(66, 99, 235, 0.15) !important;
  border-radius: 16px !important;
  box-shadow: 0 12px 32px rgba(66, 99, 235, 0.15),
              0 4px 16px rgba(0, 0, 0, 0.08) !important;
  backdrop-filter: blur(20px) !important;
  padding: 12px 16px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  color: #1a1f36 !important;
  letter-spacing: 0.2px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform: translateY(2px) scale(0.95);
  opacity: 0;

  // 渐变边框效果
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg,
      rgba(66, 99, 235, 0.2) 0%,
      rgba(142, 152, 245, 0.1) 50%,
      rgba(66, 99, 235, 0.2) 100%);
    border-radius: 16px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // 箭头样式优化
  &[data-popper-placement^="right"] {
    &::after {
      border-right-color: rgba(255, 255, 255, 0.95) !important;
      filter: drop-shadow(-2px 0 4px rgba(66, 99, 235, 0.1));
    }
  }

  // 悬停效果
  &:hover {
    transform: translateY(0) scale(1) !important;
    box-shadow: 0 16px 40px rgba(66, 99, 235, 0.2),
                0 8px 24px rgba(0, 0, 0, 0.12) !important;

    &::before {
      opacity: 1;
    }
  }
}

// 悬浮提示出现动画
@keyframes tooltipSlideIn {
  0% {
    transform: translateY(8px) scale(0.9);
    opacity: 0;
  }
  60% {
    transform: translateY(-2px) scale(1.02);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

// 悬浮提示消失动画
@keyframes tooltipSlideOut {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(4px) scale(0.95);
    opacity: 0;
  }
}

// 应用动画到悬浮提示
:deep(.tooltip-fade-enter-active) {
  animation: tooltipSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

:deep(.tooltip-fade-leave-active) {
  animation: tooltipSlideOut 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

// 菜单项悬停效果增强
.sidebar-tooltip-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateX(2px);
    background: rgba(66, 99, 235, 0.08) !important;
  }
}
</style>