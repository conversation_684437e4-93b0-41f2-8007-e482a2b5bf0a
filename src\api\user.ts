// User-related API endpoints
import request from '@/utils/request'
// import type { RequestConfig } from "@/utils/request";

// User login
export const login = (data: { username: string; password: string }) => {
  return request.post('/api/user/login', data)
}

// Get user info
export const getUserInfo = () => {
  return request.get('/api/user/info')
}

// User logout
export const logout = () => {
  return request.post('/api/user/logout')
}

// Get user list
export const getUserList = (params: { page: number; limit: number }) => {
  return request.get('/api/user/list', { params })
}

// Add user
export const addUser = (data: { username: string; password: string; role: string }) => {
  return request.post('/api/user/add', data)
}

// Update user
export const updateUser = (data: { id: number; username: string; role: string }) => {
  return request.put('/api/user/update', data)
}

// Delete user
export const deleteUser = (id: number) => {
  return request.delete(`/api/user/delete/${id}`)
}