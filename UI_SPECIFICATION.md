# SAP 后台管理系统 UI 规范文档

## 1. 设计风格

### 1.1 整体风格
- 采用现代简约设计风格
- 主题色：薰衣草紫色系 (#9370DB)
- 圆角设计：采用统一的圆角风格，增加视觉友好度
- 阴影效果：适当使用阴影提升层次感
- 动效：使用柔和的过渡动画

### 1.2 色彩规范

#### 主题色系
```css
主色：#9370DB (薰衣草紫)
浅色：#BA85D6
深色：#7B68EE
```

#### 文字颜色
```css
主要文字：#1A1F36
常规文字：#697386
次要文字：#97A3B9
占位文字：#C0C4CC
```

#### 边框颜色
```css
基础边框：#E5E7EB
浅色边框：#F3F4F6
特浅边框：#F9FAFB
```

#### 背景色
```css
基础背景：#F5F7FD
```

## 2. 组件规范

### 2.1 按钮规范

#### 尺寸
- 大号按钮：高度 48px，字号 16px
- 中号按钮：高度 40px，字号 14px
- 小号按钮：高度 32px，字号 12px

#### 按钮圆角
```css
基础圆角：12px
小型圆角：8px
大型圆角：16px
```

#### 按钮类型与样式
1. 主要按钮
```css
背景：linear-gradient(135deg, #9370DB 0%, #7B68EE 100%)
文字：白色
悬浮效果：上移2px，添加阴影
```

2. 次要按钮
```css
背景：rgba(249, 250, 251, 0.8)
边框：1px solid var(--border-color-base)
文字：主文本色
悬浮效果：上移1px，边框变为主题色
```

### 2.2 表格规范

#### 表格外观
- 圆角：12px
- 背景色：白色
- 阴影：0 1px 2px rgba(0, 0, 0, 0.05)

#### 表格内边距
- 单元格内边距：12px 16px
- 表头背景色：#F8FAFF
- 表头文字：加粗，颜色 #1A1F36

#### 表格间距
- 表格与其他元素间距：20px
- 表格与按钮组间距：16px
- 表格与搜索栏间距：20px

### 2.3 表单规范

#### 表单项间距
- 垂直间距：24px
- 标签与输入框间距：8px

#### 输入框规范
- 高度：40px
- 圆角：12px
- 边框颜色：#E5E7EB
- 聚焦边框：主题色
- 背景色：rgba(249, 250, 251, 0.8)

#### 标签样式
- 字号：14px
- 字重：500
- 颜色：#1A1F36

### 2.4 弹窗规范

#### 尺寸与间距
- 标准宽度：480px
- 内边距：头部24px 32px 16px
- 内容区：24px 32px
- 底部：16px 32px 32px

#### 样式
- 圆角：24px
- 背景：rgba(255, 255, 255, 0.95)
- 阴影：0 20px 40px rgba(0, 0, 0, 0.2)
- 毛玻璃效果：backdrop-filter: blur(20px)

## 3. 布局规范

### 3.1 页面布局
- 顶部导航高度：64px
- 侧边栏宽度：展开240px，收起64px
- 内容区内边距：20px
- 卡片间距：16px

### 3.2 响应式断点
```css
手机：< 768px
平板：768px - 1024px
桌面：1024px - 1440px
大屏：> 1440px
```

## 4. 交互规范

### 4.1 反馈效果
- 按钮点击：添加波纹效果
- 悬浮效果：统一使用上移+阴影
- 加载状态：使用统一的加载动画
- 过渡动画：使用 cubic-bezier(0.4, 0, 0.2, 1) 曲线

### 4.2 表单交互
- 输入框获焦：上移2px + 主题色边框
- 错误提示：红色边框 + 底部错误提示
- 成功提示：绿色图标 + 提示文字

## 5. 图标规范

### 5.1 图标尺寸
- 大号图标：24px
- 中号图标：20px
- 小号图标：16px

### 5.2 图标颜色
- 主要图标：继承文字颜色
- 次要图标：#697386
- 强调图标：主题色

## 6. 响应式设计

### 6.1 移动端适配
- 按钮最小点击区域：44px × 44px
- 表格横向滚动
- 弹窗适应屏幕宽度
- 侧边栏在移动端默认收起

### 6.2 间距调整
- 移动端内边距减小为原来的60%
- 组件间距在移动端统一使用12px
- 字号在移动端可适当缩小1-2px 